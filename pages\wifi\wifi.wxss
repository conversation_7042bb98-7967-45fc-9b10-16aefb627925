/* pages/wifi/wifi.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 0;
  background: linear-gradient(135deg, #2E8B57, #3CB371);
  color: white;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 当前连接状态 */
.current-status {
  border-left: 8rpx solid #2E8B57;
  background: linear-gradient(135deg, #f0f8f0, #ffffff);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 30rpx;
}

.status-icon image {
  width: 100%;
  height: 100%;
}

.status-info {
  flex: 1;
}

.wifi-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.wifi-details {
  display: flex;
  gap: 30rpx;
}

.signal-strength, .security {
  font-size: 24rpx;
  color: #666666;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #2E8B57;
  color: #2E8B57;
}

.btn-outline:active {
  background-color: #f0f8f0;
}

/* 扫描控制 */
.scan-control {
  background-color: #f8f9fa;
}

.scan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.btn-refresh {
  display: flex;
  align-items: center;
  background-color: #2E8B57;
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: none;
}

.btn-refresh image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.btn-refresh.scanning {
  background-color: #FFA500;
}

.btn-refresh.scanning image {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* WiFi列表 */
.wifi-list {
  margin-top: 30rpx;
}

.wifi-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.wifi-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.wifi-item.connected {
  border-left: 8rpx solid #2E8B57;
  background: linear-gradient(135deg, #f0f8f0, #ffffff);
}

.wifi-info {
  flex: 1;
}

.wifi-info .wifi-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.wifi-info .wifi-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

/* 信号强度条 */
.signal-bar {
  display: flex;
  align-items: flex-end;
  gap: 4rpx;
  margin-right: 15rpx;
}

.signal-bar .bar {
  width: 6rpx;
  background-color: #cccccc;
  border-radius: 3rpx;
}

.signal-bar .bar:nth-child(1) { height: 8rpx; }
.signal-bar .bar:nth-child(2) { height: 12rpx; }
.signal-bar .bar:nth-child(3) { height: 16rpx; }
.signal-bar .bar:nth-child(4) { height: 20rpx; }

.signal-bar .bar.active {
  background-color: #2E8B57;
}

.signal-text {
  font-size: 24rpx;
  color: #666666;
}

.wifi-status {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.security-icon, .connected-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2E8B57;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

/* 连接提示 */
.tips {
  background: linear-gradient(135deg, #fff8e1, #ffffff);
  border-left: 8rpx solid #FFA500;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 25rpx;
}

.tips-list {
  space-y: 20rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 密码弹窗 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eeeeee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  font-size: 50rpx;
  color: #999999;
  line-height: 1;
  cursor: pointer;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.input-group {
  position: relative;
}

.input-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.password-input {
  width: 100%;
  height: 80rpx;
  padding: 0 80rpx 0 20rpx;
  border: 2rpx solid #eeeeee;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.password-input:focus {
  border-color: #2E8B57;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
}

.password-toggle image {
  width: 100%;
  height: 100%;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
}

.modal-footer .btn {
  flex: 1;
  margin: 0;
}

/* 诊断按钮 */
.diagnosis-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eeeeee;
  text-align: center;
}

.btn-diagnosis {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFA500;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
  margin: 0 auto;
  min-width: 200rpx;
}

.btn-diagnosis:active {
  background-color: #FF8C00;
}

.diagnosis-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .modal-content {
    width: 90%;
  }
  
  .wifi-item {
    padding: 25rpx;
  }
  
  .signal-bar .bar {
    width: 4rpx;
  }
  
  .btn-diagnosis {
    padding: 15rpx 30rpx;
    font-size: 26rpx;
  }
}
