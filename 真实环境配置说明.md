# 康华医院WiFi认证小程序 - 真实环境配置说明

## 重要提醒 ⚠️

本小程序已移除所有模拟数据，现在使用真实的设备信息和用户数据。

## 需要配置的后端接口

### 1. 获取OpenID接口

**接口地址**: `http://tc.xinqiyu.cn:7111/api-get-openid.php`

**请求方式**: POST

**请求参数**:
```json
{
  "code": "微信登录返回的code"
}
```

**响应格式**:
```json
{
  "success": true,
  "openid": "用户真实的openid",
  "session_key": "会话密钥"
}
```

**PHP示例代码**:
```php
<?php
// api-get-openid.php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit();
}

$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!isset($data['code']) || empty($data['code'])) {
    echo json_encode(['success' => false, 'message' => '缺少code参数']);
    exit();
}

// 微信小程序配置
$appid = 'your_appid';        // 替换为您的小程序AppID
$secret = 'your_app_secret';  // 替换为您的小程序AppSecret

// 调用微信接口获取openid
$url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appid}&secret={$secret}&js_code={$data['code']}&grant_type=authorization_code";

$response = file_get_contents($url);
$result = json_decode($response, true);

if (isset($result['openid'])) {
    echo json_encode([
        'success' => true,
        'openid' => $result['openid'],
        'session_key' => $result['session_key']
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => '获取openid失败: ' . ($result['errmsg'] ?? '未知错误')
    ]);
}
?>
```

### 2. 更新认证接口

**接口地址**: `http://tc.xinqiyu.cn:7111/api-test.php`

**新的请求参数**:
**新的请求参数**:
```json
{
  "ip": "*************",           // 用户真实IP地址
  "mac": "02:30:D0:5C:2F:96",     // 路由器下发的真实MAC地址
  "deviceId": "Android 13_2112123AC", // 设备标识（系统+型号）
  "platform": "android",           // 平台类型
  "system": "Android 13",          // 操作系统
  "model": "2112123AC",           // 设备型号
  "openid": "真实的微信openid",     // 真实openid
  "server": "",                    // 服务器地址
  "ssid": "TP-LINK_37FF",         // WiFi网络名称
  "timestamp": 1757562228823       // 请求时间戳
}
```

**注意事项**:
- `mac`参数现在可以通过`wx.getConnectedWifi`获取路由器下发的真实MAC地址
- 新增了`deviceId`、`platform`、`system`、`model`参数用于设备识别
- `openid`现在是通过微信官方接口获取的真实值

## 小程序配置

### 1. 域名配置

在微信小程序后台配置以下域名：

**request合法域名**:
- `http://tc.xinqiyu.cn`
- `https://api.weixin.qq.com` (用于获取openid)

### 2. AppID和AppSecret

确保在后端接口中配置正确的小程序AppID和AppSecret：

```php
$appid = 'wxfb327da0d29574ca';  // 您的小程序AppID
$secret = 'your_app_secret';    // 您的小程序AppSecret
```

## MikroTik RouterOS配置

### 设备识别方案

由于无法获取真实MAC地址，建议使用以下方案进行设备识别：

1. **基于IP地址**: 使用用户的IP地址进行认证
2. **基于设备标识**: 使用`deviceId`（系统+型号）作为设备唯一标识
3. **基于OpenID**: 使用微信OpenID作为用户唯一标识

### 认证流程

1. 用户连接WiFi网络
2. 小程序获取真实IP地址和设备信息
3. 调用微信接口获取真实OpenID
4. 发送认证请求到后端
5. 后端调用MikroTik API创建用户或授权访问
6. 返回认证结果

## 测试验证

### 1. 检查OpenID获取

在小程序中查看控制台日志，确认能够获取到真实的OpenID。

### 2. 检查设备信息

确认能够获取到以下真实信息：
- IP地址
- 设备型号
- 操作系统
- 平台类型

### 3. 检查认证流程

完整测试从WiFi连接到认证成功的整个流程。

## 安全建议

1. **HTTPS**: 建议后端接口使用HTTPS协议
2. **参数验证**: 严格验证所有请求参数
3. **频率限制**: 对认证接口进行频率限制
4. **日志记录**: 记录所有认证请求和结果
5. **错误处理**: 提供友好的错误提示

## 联系支持

如果在配置过程中遇到问题，请检查：
1. 小程序AppID和AppSecret是否正确
2. 域名是否已添加到白名单
3. 后端接口是否正常响应
4. MikroTik RouterOS配置是否正确

---

**注意**: 本配置说明基于真实生产环境，请确保所有配置信息的安全性。