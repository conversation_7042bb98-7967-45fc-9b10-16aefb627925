<!--pages/index/index.wxml-->
<view class="container">
  <!-- 医院头部 -->
  <view class="hospital-header">
    <view class="hospital-name">康华医院</view>
    <view class="hospital-desc">WiFi认证服务</view>
  </view>

  <!-- WiFi连接状态 -->
  <view class="status-card">
    <view class="card-header">
      <view class="status-dot {{wifiStatus}}"></view>
      <view class="card-title">WiFi连接状态</view>
      <view class="card-action" bindtap="goToWifi">管理</view>
    </view>
    
    <view class="status-content">
      <view class="status-text {{wifiStatus}}">{{wifiStatusText}}</view>
      <view class="wifi-info" wx:if="{{currentWifi}}">
        <text class="wifi-name">{{currentWifi.SSID}}</text>
      </view>
    </view>
  </view>

  <!-- 认证状态 -->
  <view class="status-card">
    <view class="card-header">
      <view class="status-dot {{authStatus ? 'success' : 'pending'}}"></view>
      <view class="card-title">认证状态</view>
      <view class="card-action" bindtap="goToAuth">{{authStatus ? '详情' : '认证'}}</view>
    </view>
    
    <view class="status-content">
      <view class="status-text {{authStatus ? 'success' : 'pending'}}">{{authStatusText}}</view>
    </view>
  </view>

  <!-- 主要操作 -->
  <view class="main-actions">
    <button class="main-btn" bindtap="goToWifi">
      <view class="btn-text">WiFi连接</view>
    </button>
    
    <button class="main-btn primary" bindtap="goToAuth" disabled="{{!isConnected}}">
      <view class="btn-text">网络认证</view>
    </button>
  </view>
</view>