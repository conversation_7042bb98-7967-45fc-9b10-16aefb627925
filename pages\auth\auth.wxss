/* pages/auth/auth.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 50rpx 30rpx;
  background: linear-gradient(135deg, #2E8B57, #3CB371);
  color: white;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.header-icon {
  width: 100rpx;
  height: 100rpx;
  margin: 0 auto 20rpx;
}

.header-icon image {
  width: 100%;
  height: 100%;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.header-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 网络信息 */
.network-info {
  background: linear-gradient(135deg, #f0f8f0, #ffffff);
  border-left: 8rpx solid #2E8B57;
}

.info-header {
  display: flex;
  align-items: center;
}

.wifi-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 25rpx;
}

.network-details {
  flex: 1;
}

.network-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.network-status {
  font-size: 26rpx;
  color: #FFA500;
}

/* 认证信息说明 */
.auth-info {
  background: linear-gradient(135deg, #f0f8f0, #ffffff);
  border-left: 8rpx solid #2E8B57;
}

.auth-description {
  margin-top: 30rpx;
}

.desc-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;
}

.desc-item:last-child {
  margin-bottom: 0;
}

.desc-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 5rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  flex: 1;
}

/* 用户信息 */
.user-info {
  background: linear-gradient(135deg, #fff8e1, #ffffff);
  border-left: 8rpx solid #FFA500;
}

.user-profile {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
  background-color: #f0f0f0;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #666666;
  font-family: monospace;
}

/* 认证操作 */
.auth-action {
  text-align: center;
  background-color: white;
}

.auth-tips {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eeeeee;
}

.tip-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

/* 提交按钮 */
.auth-submit {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #2E8B57, #3CB371);
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
}

.auth-submit:disabled {
  background: #cccccc;
  color: #999999;
}

.auth-submit:active:not(:disabled) {
  background: linear-gradient(135deg, #228B22, #2E8B57);
}

/* 认证状态 */
.auth-status {
  text-align: center;
  padding: 50rpx 30rpx;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.success {
  background-color: #f0f8f0;
}

.status-icon.error {
  background-color: #fef0f0;
}

.status-icon image {
  width: 80rpx;
  height: 80rpx;
}

.status-text {
  margin-bottom: 40rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.status-title {
  color: #2E8B57;
}

.auth-status .status-icon.error + .status-text .status-title {
  color: #e74c3c;
}

.status-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 帮助信息 */
.help-info {
  background: linear-gradient(135deg, #fff8e1, #ffffff);
  border-left: 8rpx solid #FFA500;
}

.help-list {
  margin-top: 30rpx;
}

.help-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 5rpx;
}

.help-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

/* 协议弹窗 */
.terms-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 10%;
  left: 5%;
  right: 5%;
  bottom: 10%;
  background-color: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eeeeee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  font-size: 50rpx;
  color: #999999;
  line-height: 1;
}

.modal-body {
  flex: 1;
  overflow: hidden;
}

.terms-content {
  height: 100%;
  padding: 30rpx;
}

.terms-section {
  margin-bottom: 40rpx;
}

.terms-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.terms-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

.modal-footer {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #eeeeee;
}

.modal-footer .btn {
  width: 100%;
  margin: 0;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .code-input-group {
    flex-direction: column;
  }
  
  .btn-code {
    width: 100%;
    margin-top: 20rpx;
  }
  
  .method-item {
    padding: 25rpx;
  }
  
  .method-icon {
    width: 60rpx;
    height: 60rpx;
  }
}