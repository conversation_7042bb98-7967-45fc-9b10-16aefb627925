/* pages/index/index.wxss */
.container {
  padding: 0;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  min-height: 100vh;
}

/* 医院头部 */
.hospital-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 60rpx 30rpx 40rpx;
  text-align: center;
  color: white;
}

.hospital-name {
  font-size: 52rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.hospital-desc {
  font-size: 30rpx;
  opacity: 0.9;
}

/* 状态卡片 */
.status-card {
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.status-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.status-dot.connected {
  background: #52c41a;
}

.status-dot.disconnected {
  background: #ff4d4f;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.pending {
  background: #faad14;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.card-action {
  font-size: 30rpx;
  color: #52c41a;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: rgba(82, 196, 26, 0.1);
}

.status-content {
  padding-left: 44rpx;
}

.status-text {
  font-size: 32rpx;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.status-text.connected {
  color: #52c41a;
}

.status-text.disconnected {
  color: #ff4d4f;
}

.status-text.success {
  color: #52c41a;
}

.status-text.pending {
  color: #faad14;
}

.wifi-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.wifi-name {
  font-size: 28rpx;
  color: #666;
}

/* 主要操作按钮 */
.main-actions {
  display: flex;
  gap: 30rpx;
  margin: 40rpx 30rpx;
}

.main-btn {
  flex: 1;
  background: white;
  border: 2rpx solid #52c41a;
  border-radius: 25rpx;
  padding: 40rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #52c41a;
  transition: all 0.3s ease;
}

.main-btn.primary {
  background: #52c41a;
  color: white;
}

.main-btn:disabled {
  opacity: 0.5;
  border-color: #d9d9d9;
  color: #d9d9d9;
}

.main-btn.primary:disabled {
  background: #d9d9d9;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 动画效果 */
.status-card {
  animation: slideUp 0.5s ease-out;
}

.main-actions {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 状态指示器动画 */
.status-dot.connected {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .main-actions {
    flex-direction: column;
  }
}