<!--pages/auth/auth.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-icon">
      <image src="../../images/auth-icon.png" mode="aspectFit"></image>
    </view>
    <view class="header-title">网络认证</view>
    <view class="header-desc">完成身份认证以访问互联网</view>
  </view>

  <!-- 当前网络信息 -->
  <view class="card network-info" wx:if="{{currentWifi}}">
    <view class="info-header">
      <image src="../../images/wifi-connected.png" class="wifi-icon"></image>
      <view class="network-details">
        <view class="network-name">{{currentWifi.SSID}}</view>
        <view class="network-status">已连接 · 需要认证</view>
      </view>
    </view>
  </view>

  <!-- 微信认证说明 -->
  <view class="card auth-info">
    <view class="title">微信认证上网</view>
    <view class="auth-description">
      <view class="desc-item">
        <view class="desc-icon">🔐</view>
        <view class="desc-text">使用微信身份自动认证，无需输入账号密码</view>
      </view>
      <view class="desc-item">
        <view class="desc-icon">⚡</view>
        <view class="desc-text">一键认证，快速连接医院WiFi网络</view>
      </view>
      <view class="desc-item">
        <view class="desc-icon">🛡️</view>
        <view class="desc-text">安全可靠，保护您的隐私信息</view>
      </view>
    </view>
  </view>

  <!-- 用户信息展示 -->
  <view class="card user-info" wx:if="{{userInfo}}">
    <view class="title">当前用户</view>
    <view class="user-profile">
      <view class="user-avatar">
        <image src="{{userInfo.avatarUrl || '../../images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <view class="user-name">{{userInfo.nickName || '微信用户'}}</view>
        <view class="user-id">OpenID: {{openid ? openid.substring(0, 8) + '...' : '获取中...'}}</view>
      </view>
    </view>
  </view>

  <!-- 认证按钮 -->
  <view class="card auth-action">
    <button class="btn auth-submit" 
            bindtap="startWechatAuth" 
            disabled="{{isAuthenticating}}"
            loading="{{isAuthenticating}}">
      {{isAuthenticating ? '认证中...' : '微信一键认证上网'}}
    </button>
    
    <view class="auth-tips">
      <view class="tip-text">点击认证按钮将获取您的微信OpenID用于网络认证</view>
      <view class="tip-text">认证成功后即可正常使用医院WiFi网络</view>
    </view>
  </view>

  <!-- 认证状态 -->
  <view class="card auth-status" wx:if="{{authStatus}}">
    <view class="status-icon {{authStatus === 'success' ? 'success' : 'error'}}">
      <image src="../../images/{{authStatus === 'success' ? 'success' : 'error'}}.png" mode="aspectFit"></image>
    </view>
    <view class="status-text">
      <view class="status-title">{{authStatus === 'success' ? '认证成功' : '认证失败'}}</view>
      <view class="status-desc">{{authStatusMessage}}</view>
    </view>
    <button wx:if="{{authStatus === 'success'}}" class="btn" bindtap="testNetwork">测试网络</button>
    <button wx:if="{{authStatus === 'error'}}" class="btn btn-outline" bindtap="retryAuth">重新认证</button>
  </view>

  <!-- 帮助信息 -->
  <view class="card help-info">
    <view class="title">使用说明</view>
    <view class="help-list">
      <view class="help-item">
        <text class="help-icon">🔐</text>
        <text class="help-text">使用微信OpenID进行身份认证</text>
      </view>
      <view class="help-item">
        <text class="help-icon">📶</text>
        <text class="help-text">适用于MikroTik RouterOS 7.15.3</text>
      </view>
      <view class="help-item">
        <text class="help-icon">⚡</text>
        <text class="help-text">一键认证，快速上网</text>
      </view>
      <view class="help-item">
        <text class="help-icon">⏰</text>
        <text class="help-text">认证有效期：24小时</text>
      </view>
    </view>
  </view>
</view>

<!-- 网络使用协议弹窗 -->
<view class="terms-modal" wx:if="{{showTermsModal}}">
  <view class="modal-mask" bindtap="hideTermsModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-title">网络使用协议</view>
      <view class="modal-close" bindtap="hideTermsModal">×</view>
    </view>
    <view class="modal-body">
      <scroll-view scroll-y class="terms-content">
        <view class="terms-section">
          <view class="terms-title">1. 使用规范</view>
          <view class="terms-text">用户应当遵守国家相关法律法规，不得利用网络从事危害国家安全、泄露国家秘密等危害国家安全的活动。</view>
        </view>
        <view class="terms-section">
          <view class="terms-title">2. 禁止行为</view>
          <view class="terms-text">禁止利用网络传播违法信息，禁止攻击网络设施，禁止盗用他人网络资源。</view>
        </view>
        <view class="terms-section">
          <view class="terms-title">3. 责任声明</view>
          <view class="terms-text">医院对网络服务的稳定性和安全性不承担绝对责任，用户应当自行承担使用风险。</view>
        </view>
        <view class="terms-section">
          <view class="terms-title">4. 隐私保护</view>
          <view class="terms-text">医院承诺保护用户隐私信息，不会将用户信息用于商业用途。</view>
        </view>
      </scroll-view>
    </view>
    <view class="modal-footer">
      <button class="btn" bindtap="agreeTerms">同意协议</button>
    </view>
  </view>
</view>
