/* pages/about/about.wxss */

/* 医院横幅 */
.hospital-banner {
  position: relative;
  height: 400rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.banner-bg image {
  width: 100%;
  height: 100%;
}

.banner-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(46, 139, 87, 0.8), rgba(60, 179, 113, 0.6));
}

.banner-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 40rpx;
  color: white;
}

.hospital-logo {
  width: 120rpx;
  height: 120rpx;
  margin-right: 40rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hospital-logo image {
  width: 80rpx;
  height: 80rpx;
}

.hospital-basic {
  flex: 1;
}

.hospital-name {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.hospital-level {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.hospital-motto {
  font-size: 26rpx;
  opacity: 0.8;
  font-style: italic;
}

/* 简介内容 */
.intro-content {
  line-height: 1.8;
}

.intro-text {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 25rpx;
  text-indent: 2em;
}

.intro-text:last-child {
  margin-bottom: 0;
}

/* 联系信息 */
.contact-info {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.contact-list {
  margin-top: 30rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
  transition: background-color 0.3s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background-color: #f0f8f0;
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
}

.contact-icon image {
  width: 100%;
  height: 100%;
}

.contact-details {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.contact-action {
  width: 30rpx;
  height: 30rpx;
}

.contact-action image {
  width: 100%;
  height: 100%;
}

/* 科室网格 */
.department-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25rpx;
  margin-top: 30rpx;
}

.department-item {
  background-color: #f8f9fa;
  padding: 30rpx 20rpx;
  border-radius: 15rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.department-item:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.dept-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
}

.dept-icon image {
  width: 100%;
  height: 100%;
}

.dept-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.dept-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 荣誉列表 */
.honor-list {
  margin-top: 30rpx;
}

.honor-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.honor-item:last-child {
  border-bottom: none;
}

.honor-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 25rpx;
}

.honor-icon image {
  width: 100%;
  height: 100%;
}

.honor-content {
  flex: 1;
}

.honor-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.honor-year {
  font-size: 24rpx;
  color: #2E8B57;
  font-weight: 500;
}

/* WiFi服务说明 */
.wifi-service {
  background: linear-gradient(135deg, #f0f8f0, #ffffff);
  border-left: 8rpx solid #2E8B57;
}

.service-content {
  margin-top: 30rpx;
}

.service-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 35rpx;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
  margin-top: 5rpx;
}

.service-text {
  flex: 1;
}

.service-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 意见反馈 */
.feedback-section {
  background: linear-gradient(135deg, #fff8e1, #ffffff);
  border-left: 8rpx solid #FFA500;
}

.feedback-content {
  margin-top: 30rpx;
}

.feedback-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.feedback-contacts {
  display: flex;
  gap: 20rpx;
}

.feedback-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 20rpx;
  background-color: #2E8B57;
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 28rpx;
}

.feedback-btn:active {
  background-color: #228B22;
}

.btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

/* 二维码弹窗 */
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eeeeee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  font-size: 50rpx;
  color: #999999;
  line-height: 1;
}

.modal-body {
  padding: 50rpx 30rpx;
}

.qr-container {
  text-align: center;
}

.qr-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #eeeeee;
  border-radius: 10rpx;
}

.qr-desc {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 15rpx;
}

.qr-tip {
  font-size: 26rpx;
  color: #666666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .department-grid {
    grid-template-columns: 1fr;
  }
  
  .feedback-contacts {
    flex-direction: column;
  }
  
  .banner-content {
    padding: 30rpx;
  }
  
  .hospital-logo {
    width: 100rpx;
    height: 100rpx;
    margin-right: 30rpx;
  }
  
  .hospital-name {
    font-size: 36rpx;
  }
}