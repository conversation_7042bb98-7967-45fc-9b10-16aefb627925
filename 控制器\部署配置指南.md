# WiFi认证小程序部署配置指南

## 🚀 快速部署步骤

### 1. 配置后端API地址

#### 修改OpenID获取API地址
在 `pages/auth/auth.js` 文件中，找到第 **102** 行：
```javascript
url: 'https://your-backend-api.com/api/get-openid', // 需要替换为实际的API地址
```
替换为您的实际API地址，例如：
```javascript
url: 'https://api.kanghua.com/api/get-openid',
```

#### 修改认证API地址  
在 `pages/auth/auth.js` 文件中，找到第 **377** 行：
```javascript
url: 'https://your-backend-api.com/auth/mikrotik', // 需要替换为实际的认证API地址
```
替换为您的实际API地址，例如：
```javascript
url: 'https://api.kanghua.com/auth/mikrotik',
```

### 2. 配置微信小程序

#### 设置AppID
在 `project.config.json` 中修改：
```json
{
  "appid": "wxfb327da0d29574ca"  // 替换为您的小程序AppID
}
```

#### 配置服务器域名
在微信小程序管理后台 → 开发 → 开发管理 → 开发设置 → 服务器域名中添加：
- **request合法域名**: `https://api.kanghua.com`
- **uploadFile合法域名**: `https://api.kanghua.com`  
- **downloadFile合法域名**: `https://api.kanghua.com`

### 3. 部署后端服务

#### 使用提供的Node.js示例
1. 复制 `后端API示例/get-openid-api.js` 到您的服务器
2. 安装依赖：
```bash
npm install express axios
```
3. 修改微信小程序配置：
```javascript
const WECHAT_CONFIG = {
  appid: 'wxfb327da0d29574ca', // 您的小程序AppID
  secret: 'your_actual_app_secret' // 您的小程序AppSecret
};
```
4. 启动服务：
```bash
node get-openid-api.js
```

#### 或使用PHP示例
1. 复制 `api-get-openid.php示例.php` 到您的Web服务器
2. 修改配置：
```php
$appid = 'wxfb327da0d29574ca';  // 您的小程序AppID
$secret = 'your_actual_app_secret'; // 您的小程序AppSecret
```

## 🔧 完整的认证流程

### 用户操作流程
1. **连接WiFi**: 用户在WiFi页面连接到医院的开放网络
2. **点击认证**: 在首页点击"我要上网"按钮
3. **获取身份**: 点击"获取微信身份信息"按钮
4. **开始认证**: 点击"开始WiFi认证"按钮
5. **认证成功**: 显示认证成功，可以正常上网

### 技术实现流程
1. `wx.login()` → 获取临时登录凭证code
2. 调用后端API `/api/get-openid` → 换取OpenID
3. 收集设备信息（IP、MAC、SSID等）
4. 调用认证API `/auth/mikrotik` → 提交认证信息
5. 处理认证结果 → 保存认证状态

## 🛠️ 测试模式

### 开发测试
如果后端API暂时不可用，小程序会自动进入测试模式：
- 使用模拟OpenID（以`test_`开头）
- 模拟认证成功
- 显示"测试模式"标识

### 生产环境
确保以下配置正确：
- ✅ 后端API地址已正确配置
- ✅ 微信小程序AppID和AppSecret已配置
- ✅ 服务器域名已添加到白名单
- ✅ MikroTik路由器API已配置

## 📋 检查清单

### 小程序配置
- [ ] 修改 `pages/auth/auth.js` 中的API地址（2处）
- [ ] 设置正确的小程序AppID
- [ ] 配置服务器域名白名单

### 后端服务
- [ ] 部署OpenID获取API
- [ ] 部署WiFi认证API  
- [ ] 配置微信小程序AppSecret
- [ ] 测试API接口可用性

### MikroTik配置
- [ ] 启用Hotspot服务
- [ ] 配置API访问
- [ ] 创建用户配置文件
- [ ] 测试API连接

## 🔍 故障排查

### 常见问题
1. **OpenID获取失败**
   - 检查API地址是否正确
   - 检查AppID和AppSecret配置
   - 检查服务器域名白名单

2. **认证失败**
   - 检查WiFi是否已连接
   - 检查认证API地址
   - 查看控制台错误信息

3. **网络连接问题**
   - 检查MikroTik配置
   - 验证API接口可用性
   - 检查防火墙设置

### 调试方法
1. 打开微信开发者工具控制台
2. 查看网络请求和响应
3. 检查错误日志
4. 使用测试模式验证流程

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台错误信息
2. 网络请求状态
3. API响应内容
4. MikroTik日志

---

**注意**: 请确保在生产环境中使用HTTPS协议，保护用户数据安全。
