# 康华医院WiFi认证小程序 - 后端API配置说明

## 🔧 API配置

### 1. 修改API地址

在 `pages/wifi/wifi.js` 文件中，找到以下代码：

```javascript
// TODO: 配置您的后端API地址
const apiUrl = 'https://your-backend-api.com/auth/mikrotik'
```

将 `https://your-backend-api.com/auth/mikrotik` 替换为您的实际后端API地址。

### 2. API接口规范

#### 请求方式
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
```json
{
  "ip": "*************",        // 用户设备IP地址
  "mac": "9A:45:14:EF:2D:D8",   // 用户设备MAC地址（模拟生成）
  "openid": "test",             // 微信用户OpenID
  "server": "",                 // 服务器地址（可选）
  "ssid": "TP-LINK_37FF",       // WiFi网络名称
  "timestamp": 1704067200000    // 请求时间戳
}
```

#### 响应格式
```json
{
  "success": true,              // 认证是否成功
  "message": "认证成功",        // 响应消息
  "data": {                     // 可选的额外数据
    "userId": "user123",
    "expireTime": 86400
  }
}
```

## 🚀 后端实现示例

### Node.js + Express 示例

```javascript
const express = require('express');
const axios = require('axios');
const app = express();

app.use(express.json());

// WiFi认证接口
app.post('/auth/mikrotik', async (req, res) => {
  try {
    const { ip, mac, openid, ssid } = req.body;
    
    // 1. 验证OpenID（调用微信API或数据库验证）
    const userInfo = await validateOpenId(openid);
    if (!userInfo) {
      return res.json({
        success: false,
        message: '用户验证失败'
      });
    }
    
    // 2. 调用MikroTik API创建用户
    const mikrotikResult = await createMikroTikUser({
      username: openid,
      password: generatePassword(),
      ip: ip,
      mac: mac
    });
    
    if (mikrotikResult.success) {
      // 3. 记录认证日志
      await logAuthRecord({
        openid,
        ip,
        mac,
        ssid,
        status: 'success',
        timestamp: new Date()
      });
      
      res.json({
        success: true,
        message: '认证成功',
        data: {
          userId: openid,
          expireTime: 86400 // 24小时
        }
      });
    } else {
      res.json({
        success: false,
        message: 'MikroTik认证失败'
      });
    }
    
  } catch (error) {
    console.error('认证错误:', error);
    res.json({
      success: false,
      message: '服务器错误'
    });
  }
});

// MikroTik API调用
async function createMikroTikUser({ username, password, ip, mac }) {
  try {
    // 使用MikroTik REST API或RouterOS API
    const response = await axios.post('http://your-mikrotik-ip:8080/rest/ip/hotspot/user/add', {
      name: username,
      password: password,
      profile: 'default',
      comment: `WiFi认证用户 - ${new Date().toISOString()}`
    }, {
      auth: {
        username: 'admin',
        password: 'your-mikrotik-password'
      }
    });
    
    return { success: true, data: response.data };
  } catch (error) {
    console.error('MikroTik API错误:', error);
    return { success: false, error: error.message };
  }
}

app.listen(3000, () => {
  console.log('认证服务器启动在端口 3000');
});
```

### Python + Flask 示例

```python
from flask import Flask, request, jsonify
import requests
import hashlib
import time
import json

app = Flask(__name__)

@app.route('/auth/mikrotik', methods=['POST'])
def auth_mikrotik():
    try:
        data = request.get_json()
        ip = data.get('ip')
        mac = data.get('mac')
        openid = data.get('openid')
        ssid = data.get('ssid')
        
        # 1. 验证OpenID
        if not validate_openid(openid):
            return jsonify({
                'success': False,
                'message': '用户验证失败'
            })
        
        # 2. 创建MikroTik用户
        result = create_mikrotik_user(openid, ip, mac)
        
        if result['success']:
            # 3. 记录日志
            log_auth_record(openid, ip, mac, ssid, 'success')
            
            return jsonify({
                'success': True,
                'message': '认证成功',
                'data': {
                    'userId': openid,
                    'expireTime': 86400
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'MikroTik认证失败'
            })
            
    except Exception as e:
        print(f'认证错误: {e}')
        return jsonify({
            'success': False,
            'message': '服务器错误'
        })

def create_mikrotik_user(username, ip, mac):
    try:
        # MikroTik RouterOS API调用
        mikrotik_api_url = 'http://your-mikrotik-ip:8080/rest/ip/hotspot/user/add'
        
        response = requests.post(mikrotik_api_url, 
            json={
                'name': username,
                'password': generate_password(),
                'profile': 'default',
                'comment': f'WiFi认证用户 - {time.strftime("%Y-%m-%d %H:%M:%S")}'
            },
            auth=('admin', 'your-mikrotik-password')
        )
        
        return {'success': True, 'data': response.json()}
    except Exception as e:
        print(f'MikroTik API错误: {e}')
        return {'success': False, 'error': str(e)}

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=True)
```

## 🔐 MikroTik RouterOS 配置

### 1. 启用Hotspot服务

```bash
# 创建Hotspot配置文件
/ip hotspot profile
add dns-name=kanghua-wifi.local hotspot-address=************ html-directory=hotspot login-by=cookie,http-chap name=hsprof1

# 创建Hotspot
/ip hotspot
add address-pool=dhcp_pool1 disabled=no interface=bridge name=hotspot1 profile=hsprof1

# 创建用户配置文件
/ip hotspot user profile
add name=default rate-limit=10M/10M session-timeout=1d
```

### 2. 启用API服务

```bash
# 启用REST API
/ip service
set api disabled=no port=8728
set api-ssl disabled=no port=8729

# 启用HTTP API（如果需要）
/ip service
set www disabled=no port=8080
```

### 3. 创建API用户

```bash
# 创建专用API用户
/user
add name=api-user password=your-api-password group=full
```

## 📝 部署清单

### 1. 小程序配置
- [ ] 修改 `pages/wifi/wifi.js` 中的API地址
- [ ] 配置微信小程序域名白名单
- [ ] 测试WiFi连接和认证流程

### 2. 后端服务
- [ ] 部署后端API服务
- [ ] 配置数据库（用户记录、认证日志）
- [ ] 配置微信OpenID验证
- [ ] 测试API接口

### 3. MikroTik配置
- [ ] 配置Hotspot服务
- [ ] 启用API服务
- [ ] 创建用户配置文件
- [ ] 测试API连接

### 4. 安全配置
- [ ] 配置HTTPS证书
- [ ] 设置API访问限制
- [ ] 配置防火墙规则
- [ ] 定期更新密码

## 🔍 调试和监控

### 1. 日志记录
- 用户认证请求日志
- MikroTik API调用日志
- 错误和异常日志

### 2. 监控指标
- 认证成功率
- API响应时间
- 用户连接数量
- 网络使用统计

### 3. 故障排查
- 检查网络连通性
- 验证API配置
- 查看MikroTik日志
- 测试用户权限

---

**注意**: 请根据您的实际环境调整配置参数，确保网络安全和用户隐私保护。