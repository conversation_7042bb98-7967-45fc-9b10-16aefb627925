// pages/wifi/wifi.js
const app = getApp()
const debugWifi = require('./debug.js')
const wifiHelper = require('./helper.js')

Page({
  data: {
    wifiList: [],
    currentWifi: null,
    isScanning: false,
    showPasswordModal: false,
    selectedWifi: null,
    wifiPassword: '',
    showPassword: false,
    isConnecting: false
  },

  onLoad() {
    console.log('WiFi页面加载')
    
    // 检查系统信息和网络状态（调试用）
    debugWifi.checkSystemInfo()
    debugWifi.checkNetworkType()
    
    this.checkPermissions()
  },

  onShow() {
    console.log('WiFi页面显示')
    this.getCurrentWifi()
    this.startWifiScan()
  },

  // 检查权限
  checkPermissions() {
    // 检查位置权限
    wx.getSetting({
      success: (res) => {
        console.log('当前权限设置:', res.authSetting)
        
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户拒绝了位置权限
          wx.showModal({
            title: '需要位置权限',
            content: 'WiFi功能需要位置权限才能正常使用，请在设置中开启',
            showCancel: true,
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting()
              }
            }
          })
        } else if (res.authSetting['scope.userLocation'] === undefined) {
          // 还未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              console.log('位置权限授权成功')
              this.initWifiModule()
            },
            fail: () => {
              console.log('位置权限授权失败')
              this.showLocationPermissionTip()
            }
          })
        } else {
          // 已经有权限，直接初始化
          this.initWifiModule()
        }
      },
      fail: (err) => {
        console.error('获取权限设置失败:', err)
        this.initWifiModule()
      }
    })
  },

  // 显示位置权限提示
  showLocationPermissionTip() {
    wx.showModal({
      title: '权限说明',
      content: '由于系统限制，WiFi功能需要位置权限。这不会获取您的具体位置信息，仅用于WiFi扫描。',
      showCancel: true,
      confirmText: '重新授权',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          this.checkPermissions()
        } else {
          // 即使没有权限也尝试初始化，某些情况下可能仍然可用
          this.initWifiModule()
        }
      }
    })
  },

  // 初始化WiFi模块
  initWifiModule() {
    console.log('开始初始化WiFi模块')
    wx.startWifi({
      success: (res) => {
        console.log('WiFi模块初始化成功', res)
      },
      fail: (err) => {
        console.error('WiFi模块初始化失败', err)
        let errorMsg = 'WiFi模块初始化失败'
        
        if (err && err.errCode) {
          switch (err.errCode) {
            case 12000:
              errorMsg = '请先开启手机WiFi功能'
              break
            case 12001:
              errorMsg = '请开启GPS定位服务'
              break
            default:
              errorMsg = `WiFi初始化失败 (错误码: ${err.errCode})`
          }
        }
        
        wx.showModal({
          title: '提示',
          content: errorMsg,
          showCancel: false,
          confirmText: '我知道了'
        })
      }
    })
  },

  // 获取当前连接的WiFi
  getCurrentWifi() {
    wx.getConnectedWifi({
      success: (res) => {
        console.log('当前连接的WiFi:', res.wifi)
        this.setData({
          currentWifi: res.wifi
        })
        app.globalData.currentWifi = res.wifi
        app.globalData.isConnected = true
      },
      fail: (err) => {
        console.log('获取当前WiFi失败:', err)
        this.setData({
          currentWifi: null
        })
        app.globalData.currentWifi = null
        app.globalData.isConnected = false
      }
    })
  },

  // 开始扫描WiFi
  startWifiScan() {
    if (this.data.isScanning) return

    this.setData({
      isScanning: true,
      wifiList: []
    })

    wx.getWifiList({
      success: (res) => {
        console.log('开始扫描WiFi')
      },
      fail: (err) => {
        console.error('扫描WiFi失败:', err)
        this.setData({
          isScanning: false
        })
        wx.showToast({
          title: '扫描失败，请重试',
          icon: 'none'
        })
      }
    })

    // 监听WiFi列表获取
    wx.onGetWifiList((res) => {
      console.log('原始WiFi列表:', res.wifiList)
      
      // 过滤和处理WiFi列表
      let wifiList = res.wifiList
        .filter(wifi => {
          // 过滤无效的WiFi
          return wifi && wifi.SSID && wifi.SSID.trim() !== ''
        })
        .map(wifi => {
          // 确保每个WiFi对象都有必要的属性
          return {
            SSID: wifi.SSID,
            BSSID: wifi.BSSID || '',
            secure: wifi.secure || false,
            signalStrength: wifi.signalStrength || 0,
            frequency: wifi.frequency || 0
          }
        })
        .sort((a, b) => {
          // 优先显示医院WiFi
          const aIsHospital = a.SSID.includes('KangHua') || a.SSID.includes('康华')
          const bIsHospital = b.SSID.includes('KangHua') || b.SSID.includes('康华')
          
          if (aIsHospital && !bIsHospital) return -1
          if (!aIsHospital && bIsHospital) return 1
          
          // 按信号强度排序
          return b.signalStrength - a.signalStrength
        })

      console.log('处理后的WiFi列表:', wifiList)

      this.setData({
        wifiList: wifiList,
        isScanning: false
      })

      app.globalData.wifiList = wifiList
    })

    // 设置扫描超时
    setTimeout(() => {
      if (this.data.isScanning) {
        this.setData({
          isScanning: false
        })
      }
    }, 10000)
  },

  // 连接WiFi
  connectWifi(e) {
    const wifi = e.currentTarget.dataset.wifi
    
    // 调试信息
    debugWifi.logWifiInfo(wifi)

    // 检查WiFi对象是否有效
    if (!wifi || !wifi.SSID) {
      wx.showToast({
        title: '无效的WiFi信息',
        icon: 'none'
      })
      return
    }

    // 如果已经连接到此WiFi，则不需要重新连接
    if (this.data.currentWifi && this.data.currentWifi.SSID === wifi.SSID) {
      wx.showToast({
        title: '已连接此网络',
        icon: 'success'
      })
      return
    }

    // 检查是否需要密码
    if (wifi.secure) {
      // 安全网络，显示密码输入框
      console.log('安全网络，需要密码')
      this.setData({
        selectedWifi: wifi,
        showPasswordModal: true,
        wifiPassword: ''
      })
    } else {
      // 开放网络，直接连接
      console.log('开放网络，直接连接')
      this.doConnectWifi(wifi, null)
    }
  },

  // 执行WiFi连接
  doConnectWifi(wifi, password) {
    this.setData({
      isConnecting: true
    })

    wx.showLoading({
      title: '连接中...'
    })

    const connectConfig = {
      SSID: wifi.SSID,
      password: password || '' // 开放网络使用空字符串
    }

    // 只有在BSSID存在时才添加
    if (wifi.BSSID) {
      connectConfig.BSSID = wifi.BSSID
    }

    // 使用调试工具验证参数
    debugWifi.validateConnectParams(connectConfig)

    wx.connectWifi({
      ...connectConfig,
      success: (res) => {
        console.log('WiFi连接成功:', res)
        wx.hideLoading()
        wx.showToast({
          title: '连接成功',
          icon: 'success'
        })
        
        this.setData({
          currentWifi: wifi,
          showPasswordModal: false,
          isConnecting: false
        })

        app.globalData.currentWifi = wifi
        app.globalData.isConnected = true

        // 连接成功后提示用户可以进行认证
        setTimeout(() => {
          wx.showModal({
            title: '连接成功',
            content: 'WiFi连接成功！如需上网，请前往认证页面进行网络认证。',
            showCancel: true,
            confirmText: '去认证',
            cancelText: '稍后',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.navigateTo({
                  url: '/pages/auth/auth'
                })
              }
            }
          })
        }, 1000)
      },
      fail: (err) => {
        console.error('WiFi连接失败:', err)
        wx.hideLoading()
        this.setData({
          isConnecting: false
        })

        // 使用调试工具分析错误
        const errorMsg = debugWifi.analyzeError(err)

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })

        // 显示连接帮助
        setTimeout(() => {
          wifiHelper.showConnectionHelp(wifi, err)
        }, 1000)
      }
    })
  },

  // 检查网络认证
  checkNetworkAuth() {
    // 检查是否需要网页认证
    wx.request({
      url: 'https://www.baidu.com',
      method: 'GET',
      timeout: 5000,
      success: (res) => {
        console.log('网络连接正常')
      },
      fail: (err) => {
        console.log('可能需要网页认证:', err)
        this.showWebAuthOption()
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.getCurrentWifi()
    this.startWifiScan()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      wifiPassword: e.detail.value
    })
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 确认连接
  confirmConnect() {
    if (!this.data.wifiPassword.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }

    this.doConnectWifi(this.data.selectedWifi, this.data.wifiPassword)
  },

  // 隐藏密码弹窗
  hidePasswordModal() {
    this.setData({
      showPasswordModal: false,
      selectedWifi: null,
      wifiPassword: '',
      showPassword: false
    })
  },

  // 断开WiFi连接
  disconnectWifi() {
    wx.showModal({
      title: '确认断开',
      content: `确定要断开 ${this.data.currentWifi.SSID} 的连接吗？`,
      success: (res) => {
        if (res.confirm) {
          // 注意：小程序无法直接断开WiFi连接
          // 这里只是更新UI状态
          wx.showToast({
            title: '请在系统设置中断开WiFi',
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
  },

  // 网络诊断
  diagnoseNetwork() {
    wifiHelper.diagnoseNetwork()
  },

  // 页面卸载时停止WiFi
  onUnload() {
    wx.stopWifi({
      success: (res) => {
        console.log('WiFi模块已停止')
      }
    })
  }
})