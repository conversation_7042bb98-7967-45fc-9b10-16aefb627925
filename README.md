# 康华医院WiFi认证小程序

## 项目简介

康华医院WiFi认证小程序是一个专为医院患者、访客和员工提供便捷WiFi连接服务的微信小程序。通过该小程序，用户可以轻松连接医院WiFi网络并完成身份认证。

## 功能特性

### 🏠 首页
- 医院信息展示
- WiFi连接状态显示
- 快速服务入口
- 使用说明指引
- 医院公告信息

### 📶 WiFi连接
- 自动扫描可用WiFi网络
- 智能排序（优先显示医院WiFi）
- 信号强度可视化显示
- 安全网络密码输入
- 连接状态实时监控

### 🔐 网络认证
- **手机号认证**：适用于患者及家属
- **访客认证**：适用于临时访客
- **员工认证**：适用于医院工作人员
- 多种认证方式灵活选择
- 安全的身份验证流程

### 🏥 医院信息
- 医院基本信息
- 联系方式（电话、地址、网站）
- 重点科室介绍
- 医院荣誉展示
- WiFi服务说明
- 意见反馈渠道

## 技术架构

### 前端技术
- **框架**：微信小程序原生框架
- **语言**：JavaScript、WXML、WXSS
- **UI设计**：响应式设计，支持多种屏幕尺寸
- **主题色彩**：医院绿色主题 (#2E8B57)

### 核心API
- `wx.startWifi()` - 初始化WiFi模块
- `wx.getWifiList()` - 获取WiFi列表
- `wx.connectWifi()` - 连接WiFi网络
- `wx.getConnectedWifi()` - 获取当前连接的WiFi
- `wx.onGetWifiList()` - 监听WiFi列表获取

### 页面结构
```
pages/
├── index/          # 首页
│   ├── index.wxml
│   ├── index.js
│   └── index.wxss
├── wifi/           # WiFi连接页
│   ├── wifi.wxml
│   ├── wifi.js
│   └── wifi.wxss
├── auth/           # 网络认证页
│   ├── auth.wxml
│   ├── auth.js
│   └── auth.wxss
└── about/          # 关于医院页
    ├── about.wxml
    ├── about.js
    └── about.wxss
```

## 安装部署

### 环境要求
- 微信开发者工具
- 微信小程序开发账号
- 支持WiFi API的设备

### 部署步骤
1. 下载项目代码
2. 使用微信开发者工具打开项目
3. 配置小程序AppID
4. 上传代码到微信后台
5. 提交审核并发布

### 配置说明
1. 修改 `project.config.json` 中的 `appid`
2. 根据实际情况调整医院信息
3. 替换相关图片资源
4. 配置服务器域名（如需要）

## 使用说明

### 用户操作流程
1. **打开小程序** → 查看首页信息
2. **连接WiFi** → 选择医院WiFi网络
3. **身份认证** → 选择合适的认证方式
4. **完成认证** → 开始使用网络服务

### 认证方式说明
- **手机号认证**：输入手机号码，接收验证码完成认证
- **访客认证**：填写基本信息，同意使用协议完成认证
- **员工认证**：使用工号和密码登录完成认证

## 项目特色

### 🎨 用户体验
- 简洁直观的界面设计
- 流畅的操作体验
- 清晰的状态反馈
- 友好的错误提示

### 🔒 安全保障
- 安全的WiFi连接机制
- 用户隐私信息保护
- 网络使用协议约束
- 多重身份验证

### 📱 响应式设计
- 适配不同屏幕尺寸
- 优化触摸操作体验
- 合理的布局结构
- 美观的视觉效果

### ⚡ 性能优化
- 快速的页面加载
- 高效的网络请求
- 智能的缓存机制
- 流畅的动画效果

## 维护更新

### 版本管理
- 定期更新小程序版本
- 及时修复发现的问题
- 持续优化用户体验
- 添加新功能特性

### 技术支持
- 提供24小时技术支持
- 快速响应用户反馈
- 定期进行系统维护
- 保障服务稳定运行

## 联系方式

- **医院总机**：0769-22222222
- **技术支持**：通过小程序内反馈功能
- **官方网站**：www.kanghua.com

## 开源协议

本项目采用 MIT 开源协议，欢迎贡献代码和提出建议。

---

**康华医院** - 智慧医疗，贴心服务