// app.js
App({
  onLaunch() {
    console.log('康华医院WiFi认证小程序启动')
    
    // 检查小程序版本更新
    this.checkForUpdate()
    
    // 初始化全局数据
    this.globalData = {
      userInfo: null,
      wifiList: [],
      currentWifi: null,
      isConnected: false,
      hospitalInfo: {
        name: '康华医院',
        address: '广东省东莞市康华路1号',
        phone: '0769-22222222',
        website: 'www.kanghua.com'
      }
    }
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(msg) {
    console.error('小程序错误:', msg)
  },

  // 检查更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      })
    }
  },

  globalData: {}
})