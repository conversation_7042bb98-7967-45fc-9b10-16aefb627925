// pages/about/about.js
const app = getApp()

Page({
  data: {
    hospitalInfo: {},
    showQRModal: false,
    departments: [
      {
        id: 1,
        name: '心血管内科',
        icon: 'cardiology',
        description: '心脏病诊疗中心'
      },
      {
        id: 2,
        name: '神经外科',
        icon: 'neurology',
        description: '脑部疾病专科'
      },
      {
        id: 3,
        name: '骨科',
        icon: 'orthopedics',
        description: '骨骼疾病治疗'
      },
      {
        id: 4,
        name: '妇产科',
        icon: 'gynecology',
        description: '妇女儿童健康'
      },
      {
        id: 5,
        name: '急诊科',
        icon: 'emergency',
        description: '24小时急救'
      },
      {
        id: 6,
        name: '肿瘤科',
        icon: 'oncology',
        description: '肿瘤综合治疗'
      }
    ],
    honors: [
      {
        id: 1,
        title: '全国百佳医院',
        year: '2023年'
      },
      {
        id: 2,
        title: '省级文明单位',
        year: '2022年'
      },
      {
        id: 3,
        title: '医疗质量优秀奖',
        year: '2022年'
      },
      {
        id: 4,
        title: '患者满意度金奖',
        year: '2021年'
      },
      {
        id: 5,
        title: '科技创新先进单位',
        year: '2021年'
      }
    ]
  },

  onLoad() {
    console.log('关于页面加载')
    this.initData()
  },

  onShow() {
    console.log('关于页面显示')
  },

  // 初始化数据
  initData() {
    this.setData({
      hospitalInfo: app.globalData.hospitalInfo
    })
  },

  // 拨打电话
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone || this.data.hospitalInfo.phone
    
    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${phone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone,
            success: () => {
              console.log('拨打电话成功')
            },
            fail: (err) => {
              console.error('拨打电话失败:', err)
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 打开位置
  openLocation() {
    const { hospitalInfo } = this.data
    
    wx.showModal({
      title: '查看位置',
      content: '是否在地图中查看医院位置？',
      success: (res) => {
        if (res.confirm) {
          // 这里使用模拟的经纬度，实际应用中需要使用真实坐标
          wx.openLocation({
            latitude: 23.0489,
            longitude: 113.7447,
            name: hospitalInfo.name,
            address: hospitalInfo.address,
            scale: 15,
            success: () => {
              console.log('打开地图成功')
            },
            fail: (err) => {
              console.error('打开地图失败:', err)
              wx.showToast({
                title: '打开地图失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 打开网站
  openWebsite() {
    const { hospitalInfo } = this.data
    
    wx.showModal({
      title: '访问官网',
      content: `即将访问 ${hospitalInfo.website}`,
      success: (res) => {
        if (res.confirm) {
          // 小程序中无法直接打开外部网站，这里提示用户
          wx.showModal({
            title: '提示',
            content: '请在浏览器中访问医院官网，网址已复制到剪贴板',
            showCancel: false,
            success: () => {
              wx.setClipboardData({
                data: `https://${hospitalInfo.website}`,
                success: () => {
                  wx.showToast({
                    title: '网址已复制',
                    icon: 'success'
                  })
                }
              })
            }
          })
        }
      }
    })
  },

  // 显示二维码
  showQRCode() {
    this.setData({
      showQRModal: true
    })
  },

  // 隐藏二维码
  hideQRCode() {
    this.setData({
      showQRModal: false
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '康华医院WiFi认证',
      path: '/pages/index/index',
      imageUrl: '../../images/share-bg.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '康华医院WiFi认证小程序',
      imageUrl: '../../images/share-bg.png'
    }
  }
})