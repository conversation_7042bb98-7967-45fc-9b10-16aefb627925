# 康华医院WiFi认证小程序使用指南

## 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序开发账号
- 准备必要的图片资源

### 2. 项目配置
1. 打开微信开发者工具
2. 导入项目，选择项目根目录
3. 修改 `project.config.json` 中的 `appid` 为你的小程序ID
4. （可选）将图片资源放入 `images` 目录以美化界面

### 3. 医院信息配置
在 `app.js` 中修改医院基本信息：
```javascript
hospitalInfo: {
  name: '你的医院名称',
  address: '医院详细地址',
  phone: '医院联系电话',
  website: '医院官网地址'
}
```

### 4. 功能测试
- 在开发者工具中预览各个页面
- 测试WiFi连接功能（需要真机测试）
- 验证认证流程是否正常

## 核心功能说明

### WiFi连接功能
小程序使用微信提供的WiFi API：
- `wx.startWifi()` - 初始化WiFi模块
- `wx.getWifiList()` - 获取可用WiFi列表
- `wx.connectWifi()` - 连接指定WiFi
- `wx.getConnectedWifi()` - 获取当前连接状态

### 认证系统
提供三种认证方式：
1. **手机号认证** - 通过短信验证码验证身份
2. **访客认证** - 填写基本信息完成临时认证
3. **员工认证** - 使用工号密码登录系统

### 页面导航
使用微信小程序的 TabBar 导航：
- 首页：展示医院信息和WiFi状态
- WiFi连接：管理WiFi连接
- 关于医院：医院详细信息

## 自定义配置

### 修改主题色彩
在 `app.wxss` 中修改CSS变量：
```css
.hospital-primary {
  color: #你的主色调;
}
.hospital-bg {
  background-color: #你的主色调;
}
```

### 添加新的认证方式
1. 在 `pages/auth/auth.js` 中添加新的认证逻辑
2. 在 `pages/auth/auth.wxml` 中添加对应的UI界面
3. 在 `pages/auth/auth.wxss` 中添加样式

### 自定义科室信息
在 `pages/about/about.js` 中修改 `departments` 数组：
```javascript
departments: [
  {
    id: 1,
    name: '科室名称',
    icon: '图标文件名',
    description: '科室描述'
  }
]
```

## 部署发布

### 1. 代码审查
- 检查所有页面功能是否正常
- 确认图片资源是否完整
- 测试各种异常情况处理

### 2. 上传代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后在微信公众平台查看

### 3. 提交审核
1. 登录微信公众平台
2. 进入版本管理，提交审核
3. 填写审核信息，等待审核结果

### 4. 发布上线
审核通过后，在微信公众平台发布小程序

## 常见问题

### Q: WiFi功能在开发者工具中无法测试？
A: WiFi相关API需要在真机上测试，开发者工具无法模拟WiFi环境。

### Q: 如何处理网络认证？
A: 小程序连接WiFi后，如果需要网页认证，会自动跳转到认证页面。

### Q: 没有图片资源可以运行吗？
A: 可以。当前版本已优化为无图片依赖，可直接运行。如需美化界面，可后续添加图片资源。

### Q: 图片资源过大怎么办？
A: 使用图片压缩工具优化，建议单个图片不超过100KB。

### Q: 如何添加更多医院信息？
A: 在对应页面的data中添加数据，并在模板中显示。

## 技术支持

### 开发文档
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [WiFi API文档](https://developers.weixin.qq.com/miniprogram/dev/api/device/wifi/wx.startWifi.html)

### 社区支持
- 微信开发者社区
- GitHub Issues
- 技术交流群

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基础WiFi连接功能
- 添加三种认证方式
- 完成医院信息展示

---

如有其他问题，请通过小程序内的反馈功能联系我们。