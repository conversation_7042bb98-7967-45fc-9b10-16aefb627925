<!--pages/wifi/wifi.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">WiFi连接</view>
    <view class="header-desc">选择并连接医院WiFi网络</view>
  </view>

  <!-- 当前连接状态 -->
  <view class="card current-status" wx:if="{{currentWifi}}">
    <view class="status-header">
      <view class="status-icon connected">
        <image src="../../images/wifi-connected.png" mode="aspectFit"></image>
      </view>
      <view class="status-info">
        <view class="wifi-name">{{currentWifi.SSID}}</view>
        <view class="wifi-details">
          <text class="signal-strength">信号强度: {{currentWifi.signalStrength}}%</text>
          <text class="security">{{currentWifi.secure ? '安全连接' : '开放网络'}}</text>
        </view>
      </view>
    </view>
    <button class="btn btn-outline" bindtap="disconnectWifi">断开连接</button>
  </view>

  <!-- 扫描控制 -->
  <view class="card scan-control">
    <view class="scan-header">
      <view class="scan-title">可用网络</view>
      <button class="btn-refresh {{isScanning ? 'scanning' : ''}}" bindtap="startWifiScan">
        <image src="../../images/refresh.png" mode="aspectFit"></image>
        {{isScanning ? '扫描中...' : '刷新'}}
      </button>
    </view>
  </view>

  <!-- WiFi列表 -->
  <view class="wifi-list" wx:if="{{wifiList.length > 0}}">
    <view class="wifi-item {{item.SSID === currentWifi.SSID ? 'connected' : ''}}" 
          wx:for="{{wifiList}}" 
          wx:key="SSID" 
          bindtap="connectWifi" 
          data-wifi="{{item}}">
      <view class="wifi-info">
        <view class="wifi-name">{{item.SSID}}</view>
        <view class="wifi-details">
          <view class="signal-bar">
            <view class="bar {{item.signalStrength > 25 ? 'active' : ''}}"></view>
            <view class="bar {{item.signalStrength > 50 ? 'active' : ''}}"></view>
            <view class="bar {{item.signalStrength > 75 ? 'active' : ''}}"></view>
            <view class="bar {{item.signalStrength > 90 ? 'active' : ''}}"></view>
          </view>
          <text class="signal-text">{{item.signalStrength}}%</text>
        </view>
      </view>
      <view class="wifi-status">
        <image wx:if="{{item.secure}}" src="../../images/lock.png" class="security-icon"></image>
        <image wx:if="{{item.SSID === currentWifi.SSID}}" src="../../images/check.png" class="connected-icon"></image>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isScanning && wifiList.length === 0}}">
    <image src="../../images/no-wifi.png" mode="aspectFit"></image>
    <view class="empty-title">未发现WiFi网络</view>
    <view class="empty-desc">请确保WiFi功能已开启，然后点击刷新重新扫描</view>
    <button class="btn" bindtap="startWifiScan">重新扫描</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isScanning}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">正在扫描WiFi网络...</view>
  </view>

  <!-- 连接提示 -->
  <view class="card tips">
    <view class="tips-title">连接提示</view>
    <view class="tips-list">
      <view class="tip-item">
        <text class="tip-icon">💡</text>
        <text class="tip-text">推荐连接"KangHua-WiFi"网络</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🔒</text>
        <text class="tip-text">安全网络需要密码认证</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">📶</text>
        <text class="tip-text">选择信号强度较好的网络</text>
      </view>
    </view>
    
    <!-- 诊断按钮 -->
    <view class="diagnosis-section">
      <button class="btn-diagnosis" bindtap="diagnoseNetwork">
        <image src="../../images/diagnosis.png" class="diagnosis-icon"></image>
        网络诊断
      </button>
    </view>
  </view>
</view>

<!-- 密码输入弹窗 -->
<view class="password-modal" wx:if="{{showPasswordModal}}">
  <view class="modal-mask" bindtap="hidePasswordModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-title">连接到 {{selectedWifi.SSID}}</view>
      <view class="modal-close" bindtap="hidePasswordModal">×</view>
    </view>
    <view class="modal-body">
      <view class="input-group">
        <view class="input-label">WiFi密码</view>
        <input class="password-input" 
               type="{{showPassword ? 'text' : 'password'}}" 
               placeholder="请输入WiFi密码" 
               value="{{wifiPassword}}" 
               bindinput="onPasswordInput" />
        <view class="password-toggle" bindtap="togglePasswordVisibility">
          <image src="../../images/{{showPassword ? 'eye-off' : 'eye'}}.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hidePasswordModal">取消</button>
      <button class="btn" bindtap="confirmConnect" disabled="{{!wifiPassword}}">连接</button>
    </view>
  </view>
</view>