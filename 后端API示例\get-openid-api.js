// Node.js + Express 获取OpenID API示例
const express = require('express');
const axios = require('axios');
const app = express();

// 中间件
app.use(express.json());
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type');
  next();
});

// 微信小程序配置 - 请替换为您的真实配置
const WECHAT_CONFIG = {
  appid: 'wxfb327da0d29574ca', // 您的小程序AppID
  secret: 'your_app_secret_here' // 您的小程序AppSecret
};

// 获取OpenID接口
app.post('/api/get-openid', async (req, res) => {
  try {
    const { code } = req.body;
    
    // 验证参数
    if (!code) {
      return res.json({
        success: false,
        message: '缺少code参数'
      });
    }

    // 验证配置
    if (WECHAT_CONFIG.secret === 'your_app_secret_here') {
      return res.json({
        success: false,
        message: '请先配置小程序AppSecret'
      });
    }

    // 调用微信接口获取OpenID
    const wechatUrl = 'https://api.weixin.qq.com/sns/jscode2session';
    const params = {
      appid: WECHAT_CONFIG.appid,
      secret: WECHAT_CONFIG.secret,
      js_code: code,
      grant_type: 'authorization_code'
    };

    console.log('调用微信接口获取OpenID...');
    const response = await axios.get(wechatUrl, { params });
    
    if (response.data.errcode) {
      const errorMessages = {
        40013: 'AppID无效',
        40125: 'AppSecret无效',
        40029: 'code无效',
        45011: 'API调用太频繁，请稍后再试',
        40226: 'code已被使用'
      };
      
      const errorMsg = errorMessages[response.data.errcode] || 
                      `微信接口错误: ${response.data.errmsg}`;
      
      return res.json({
        success: false,
        message: errorMsg
      });
    }

    if (!response.data.openid) {
      return res.json({
        success: false,
        message: '微信接口未返回openid'
      });
    }

    // 成功获取OpenID
    const result = {
      success: true,
      openid: response.data.openid,
      message: '获取OpenID成功'
    };

    // 可选：保存用户信息到数据库
    // await saveUserToDatabase(response.data.openid, response.data.session_key);

    // 记录日志
    console.log('OpenID获取成功:', {
      openid: response.data.openid,
      timestamp: new Date().toISOString(),
      ip: req.ip
    });

    res.json(result);

  } catch (error) {
    console.error('获取OpenID失败:', error);
    res.json({
      success: false,
      message: '服务器错误'
    });
  }
});

// WiFi认证接口
app.post('/auth/mikrotik', async (req, res) => {
  try {
    const { openid, ip, mac, ssid, timestamp } = req.body;
    
    console.log('收到认证请求:', req.body);

    // 验证必需参数
    if (!openid) {
      return res.json({
        success: false,
        message: '缺少OpenID参数'
      });
    }

    // 验证OpenID有效性（这里可以查询数据库验证）
    if (!validateOpenId(openid)) {
      return res.json({
        success: false,
        message: '无效的OpenID'
      });
    }

    // 调用MikroTik API创建用户（示例）
    const mikrotikResult = await createMikroTikUser({
      username: openid,
      password: generatePassword(),
      ip: ip,
      mac: mac
    });

    if (mikrotikResult.success) {
      // 记录认证日志
      console.log('认证成功:', {
        openid,
        ip,
        mac,
        ssid,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: '认证成功',
        data: {
          userId: openid,
          expireTime: 86400, // 24小时
          expires: Date.now() + 24 * 60 * 60 * 1000
        }
      });
    } else {
      res.json({
        success: false,
        message: 'MikroTik认证失败'
      });
    }

  } catch (error) {
    console.error('认证错误:', error);
    res.json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 验证OpenID有效性
function validateOpenId(openid) {
  if (!openid || openid.trim() === '') {
    return false;
  }
  
  // 测试模式下的OpenID
  if (openid.startsWith('test_') || openid.startsWith('mock_')) {
    return true;
  }
  
  // 微信OpenID通常是28位字符
  if (openid.length < 20) {
    return false;
  }
  
  return true;
}

// 生成随机密码
function generatePassword() {
  return 'wechat_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// MikroTik用户创建（示例实现）
async function createMikroTikUser({ username, password, ip, mac }) {
  try {
    // 这里应该调用实际的MikroTik API
    // 示例使用模拟实现
    console.log('创建MikroTik用户:', { username, password, ip, mac });
    
    // 模拟API调用
    return { success: true, data: { userId: username } };
    
  } catch (error) {
    console.error('MikroTik API错误:', error);
    return { success: false, error: error.message };
  }
}

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`认证服务器启动在端口 ${PORT}`);
  console.log(`OpenID API: http://localhost:${PORT}/api/get-openid`);
  console.log(`认证API: http://localhost:${PORT}/auth/mikrotik`);
});

module.exports = app;
