// 网络诊断工具
const NetworkDiagnostic = {
  
  // 诊断网络连接
  diagnoseNetwork() {
    return new Promise((resolve) => {
      const results = {
        wifiConnected: false,
        internetAccess: false,
        httpsSupport: false,
        domainResolution: false,
        serverReachable: false,
        details: []
      }
      
      // 1. 检查WiFi连接
      wx.getNetworkType({
        success: (res) => {
          results.wifiConnected = res.networkType === 'wifi'
          results.details.push(`网络类型: ${res.networkType}`)
          
          if (results.wifiConnected) {
            // 2. 检查互联网访问
            this.testInternetAccess().then(internetOk => {
              results.internetAccess = internetOk
              
              if (internetOk) {
                // 3. 检查HTTPS支持
                this.testHttpsSupport().then(httpsOk => {
                  results.httpsSupport = httpsOk
                  
                  // 4. 检查域名解析
                  this.testDomainResolution().then(domainOk => {
                    results.domainResolution = domainOk
                    
                    // 5. 检查服务器可达性
                    this.testServerReachability().then(serverOk => {
                      results.serverReachable = serverOk
                      resolve(results)
                    })
                  })
                })
              } else {
                resolve(results)
              }
            })
          } else {
            results.details.push('未连接WiFi网络')
            resolve(results)
          }
        },
        fail: () => {
          results.details.push('无法获取网络状态')
          resolve(results)
        }
      })
    })
  },
  
  // 测试互联网访问
  testInternetAccess() {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://www.baidu.com',
        method: 'GET',
        timeout: 5000,
        success: () => {
          resolve(true)
        },
        fail: (err) => {
          console.log('互联网访问测试失败:', err)
          resolve(false)
        }
      })
    })
  },
  
  // 测试HTTPS支持
  testHttpsSupport() {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://httpbin.org/get',
        method: 'GET',
        timeout: 5000,
        success: () => {
          resolve(true)
        },
        fail: (err) => {
          console.log('HTTPS支持测试失败:', err)
          resolve(false)
        }
      })
    })
  },
  
  // 测试域名解析
  testDomainResolution() {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://kh.gh-life.com',
        method: 'GET',
        timeout: 3000,
        success: () => {
          resolve(true)
        },
        fail: (err) => {
          console.log('域名解析测试失败:', err)
          resolve(false)
        }
      })
    })
  },
  
  // 测试服务器可达性
  testServerReachability() {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://kh.gh-life.com/api-get-openid.php',
        method: 'POST',
        data: { test: true },
        timeout: 3000,
        success: () => {
          resolve(true)
        },
        fail: (err) => {
          console.log('服务器可达性测试失败:', err)
          resolve(false)
        }
      })
    })
  },
  
  // 显示诊断结果
  showDiagnosticResults(results) {
    let message = '网络诊断结果:\n\n'
    
    message += `WiFi连接: ${results.wifiConnected ? '✓' : '✗'}\n`
    message += `互联网访问: ${results.internetAccess ? '✓' : '✗'}\n`
    message += `HTTPS支持: ${results.httpsSupport ? '✓' : '✗'}\n`
    message += `域名解析: ${results.domainResolution ? '✓' : '✗'}\n`
    message += `服务器可达: ${results.serverReachable ? '✓' : '✗'}\n\n`
    
    if (results.details.length > 0) {
      message += '详细信息:\n' + results.details.join('\n')
    }
    
    // 提供解决建议
    if (!results.internetAccess) {
      message += '\n\n建议: 请检查网络连接或进行网页认证'
    } else if (!results.httpsSupport) {
      message += '\n\n建议: 网络不支持HTTPS，请联系网络管理员'
    } else if (!results.domainResolution) {
      message += '\n\n建议: 域名解析失败，请检查DNS设置'
    } else if (!results.serverReachable) {
      message += '\n\n建议: 服务器不可达，请联系技术支持'
    }
    
    wx.showModal({
      title: '网络诊断',
      content: message,
      showCancel: false,
      confirmText: '知道了'
    })
  }
}

module.exports = NetworkDiagnostic