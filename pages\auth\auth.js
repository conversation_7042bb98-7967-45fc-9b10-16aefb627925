const NetworkDiagnostic = require('../../utils/network-diagnostic.js')

Page({
  data: {
    currentWifi: null,
    userInfo: null,
    openid: '',
    isAuthenticating: false,
    authStatus: null, // 'success' | 'error' | null
    authStatusMessage: '',
    showTermsModal: false,
    hasAgreedTerms: false
  },

  onLoad: function (options) {
    console.log('Auth page loaded')
    this.getCurrentWifiInfo()
    this.getUserInfo()
    this.getOpenId()
  },

  onShow: function () {
    // 页面显示时刷新WiFi信息
    this.getCurrentWifiInfo()
  },

  // 获取当前WiFi信息
  getCurrentWifiInfo: function () {
    const app = getApp()
    if (app.globalData.currentWifi) {
      this.setData({
        currentWifi: app.globalData.currentWifi
      })
    } else {
      // 尝试重新获取WiFi信息
      wx.getConnectedWifi({
        success: (res) => {
          console.log('获取WiFi信息成功:', res.wifi)
          this.setData({
            currentWifi: res.wifi
          })
          app.globalData.currentWifi = res.wifi
        },
        fail: (err) => {
          console.error('获取WiFi信息失败:', err)
          wx.showToast({
            title: '无法获取WiFi信息',
            icon: 'none'
          })
        }
      })
    }
  },

  // 获取用户信息
  getUserInfo: function () {
    const app = getApp()
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    }
  },

  // 获取OpenID - 使用正确的微信登录流程
  getOpenId: function () {
    const app = getApp()
    if (app.globalData.openid) {
      this.setData({
        openid: app.globalData.openid
      })
      return
    }

    // 调用微信登录获取code
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('获取登录code成功:', res.code)
          this.exchangeCodeForOpenId(res.code)
        } else {
          console.error('获取登录code失败:', res.errMsg)
          this.handleOpenIdError('获取登录凭证失败')
        }
      },
      fail: (err) => {
        console.error('微信登录失败:', err)
        this.handleOpenIdError('微信登录失败')
      }
    })
  },

  // 使用code换取OpenID
  exchangeCodeForOpenId: function(code) {
    wx.showLoading({
      title: '获取用户信息...'
    })

    // 调用后端API获取OpenID
    wx.request({
      url: 'https://kh.kingben-cn.com/get-openid', // 需要替换为实际的API地址
      method: 'POST',
      data: {
        code: code
      },
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading()
        console.log('OpenID API响应:', res)

        if (res.statusCode === 200 && res.data.success && res.data.openid) {
          const openid = res.data.openid
          this.setData({
            openid: openid
          })

          const app = getApp()
          app.globalData.openid = openid

          console.log('获取OpenID成功:', openid)
          wx.showToast({
            title: '获取用户信息成功',
            icon: 'success'
          })
        } else {
          console.error('OpenID API返回错误:', res.data)
          this.handleOpenIdError(res.data.message || '获取用户信息失败')
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('OpenID API请求失败:', err)
        // 在开发测试阶段，如果API不可用，使用模拟OpenID
        if (err.errMsg && err.errMsg.includes('request:fail')) {
          console.log('API不可用，使用测试模式')
          this.useTestOpenId()
        } else {
          this.handleOpenIdError('网络请求失败')
        }
      }
    })
  },

  // 处理OpenID获取错误
  handleOpenIdError: function(message) {
    wx.showModal({
      title: '获取用户信息失败',
      content: message + '\n\n是否使用测试模式继续？',
      showCancel: true,
      confirmText: '测试模式',
      cancelText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.useTestOpenId()
        } else {
          this.getOpenId() // 重试
        }
      }
    })
  },

  // 使用测试OpenID（仅用于开发测试）
  useTestOpenId: function() {
    const testOpenId = 'test_' + Date.now().toString(36)
    this.setData({
      openid: testOpenId
    })

    const app = getApp()
    app.globalData.openid = testOpenId

    console.log('使用测试OpenID:', testOpenId)
    wx.showToast({
      title: '测试模式已启用',
      icon: 'none'
    })
  },

  // 开始微信认证
  startWechatAuth: function () {
    // 1. 检查是否同意协议
    if (!this.data.hasAgreedTerms) {
      this.setData({
        showTermsModal: true
      })
      return
    }

    // 2. 检查WiFi连接
    if (!this.data.currentWifi) {
      wx.showToast({
        title: '请先连接WiFi网络',
        icon: 'none'
      })
      return
    }

    // 3. 检查OpenID
    if (!this.data.openid) {
      wx.showModal({
        title: '需要获取用户信息',
        content: '认证需要获取您的微信用户信息，是否继续？',
        showCancel: true,
        confirmText: '获取信息',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.getOpenId()
          }
        }
      })
      return
    }

    // 4. 验证OpenID有效性
    if (!this.validateOpenId(this.data.openid)) {
      wx.showModal({
        title: '用户信息无效',
        content: '用户信息已过期，需要重新获取',
        showCancel: false,
        confirmText: '重新获取',
        success: () => {
          this.getOpenId()
        }
      })
      return
    }

    // 5. 开始认证
    this.performAuthentication()
  },

  // 验证OpenID有效性
  validateOpenId: function(openid) {
    if (!openid || openid.trim() === '') {
      return false
    }

    // 检查是否是测试OpenID
    if (openid.startsWith('test_') || openid.startsWith('mock_')) {
      return true // 测试模式下认为有效
    }

    // 检查OpenID格式（微信OpenID通常是28位字符）
    if (openid.length < 20) {
      return false
    }

    return true
  },

  // 执行认证
  performAuthentication: function () {
    this.setData({
      isAuthenticating: true,
      authStatus: null
    })

    wx.showLoading({
      title: '正在获取设备信息...'
    })

    // 获取设备信息后进行认证
    this.getDeviceInfo()
      .then((deviceInfo) => {
        wx.showLoading({
          title: '认证中...'
        })
        return this.authenticateWithMikroTik(deviceInfo)
      })
      .catch((error) => {
        console.error('认证过程出错:', error)
        wx.hideLoading()
        this.handleAuthError(error.message || '认证过程出错')
      })
  },

  // 获取设备信息
  getDeviceInfo: function() {
    return new Promise((resolve, reject) => {
      const deviceInfo = {
        openid: this.data.openid,
        ssid: this.data.currentWifi.SSID || '',
        bssid: this.data.currentWifi.BSSID || '',
        timestamp: Date.now()
      }

      // 获取设备IP地址
      this.getDeviceIP()
        .then((ip) => {
          deviceInfo.ip = ip
          console.log('设备信息收集完成:', deviceInfo)
          resolve(deviceInfo)
        })
        .catch((error) => {
          console.warn('获取IP地址失败，使用默认值:', error)
          deviceInfo.ip = '*************' // 默认IP
          resolve(deviceInfo)
        })
    })
  },

  // 获取设备IP地址
  getDeviceIP: function() {
    return new Promise((resolve, reject) => {
      // 尝试通过网络请求获取真实IP
      wx.request({
        url: 'https://api.ipify.org?format=json',
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          if (res.statusCode === 200 && res.data.ip) {
            resolve(res.data.ip)
          } else {
            reject(new Error('IP服务响应异常'))
          }
        },
        fail: (err) => {
          // 如果外网IP获取失败，尝试获取本地网络信息
          wx.getNetworkType({
            success: (res) => {
              // 根据网络类型生成模拟IP
              const networkType = res.networkType
              let mockIP = '192.168.1.' + Math.floor(Math.random() * 200 + 10)

              if (networkType === 'wifi') {
                mockIP = '192.168.' + Math.floor(Math.random() * 255) + '.' + Math.floor(Math.random() * 200 + 10)
              }

              console.log('使用模拟IP:', mockIP)
              resolve(mockIP)
            },
            fail: () => {
              reject(new Error('无法获取网络信息'))
            }
          })
        }
      })
    })
  },

  // MikroTik认证
  authenticateWithMikroTik: function (deviceInfo) {
    return new Promise((resolve, reject) => {
      const authData = {
        openid: deviceInfo.openid,
        ip: deviceInfo.ip,
        mac: deviceInfo.bssid, // 使用WiFi的BSSID作为MAC地址参考
        ssid: deviceInfo.ssid,
        timestamp: deviceInfo.timestamp,
        server: '', // 服务器地址，如果需要的话
      }

      console.log('提交认证数据:', authData)

      // 调用认证API
      wx.request({
        url: 'https://your-backend-api.com/auth/mikrotik', // 需要替换为实际的认证API地址
        method: 'POST',
        data: authData,
        header: {
          'content-type': 'application/json'
        },
        timeout: 10000, // 10秒超时
        success: (res) => {
          console.log('认证API响应:', res)
          wx.hideLoading()

          if (res.statusCode === 200 && res.data.success) {
            this.setData({
              isAuthenticating: false,
              authStatus: 'success',
              authStatusMessage: res.data.message || '认证成功！您现在可以正常使用网络了。'
            })

            wx.showToast({
              title: '认证成功',
              icon: 'success'
            })

            // 保存认证状态
            const authStatus = {
              status: 'success',
              authenticated: true,
              timestamp: Date.now(),
              openid: deviceInfo.openid,
              ip: deviceInfo.ip,
              ssid: deviceInfo.ssid,
              expires: res.data.expires || (Date.now() + 24 * 60 * 60 * 1000) // 默认24小时过期
            }

            wx.setStorageSync('wifiAuthStatus', authStatus)
            resolve(authStatus)

          } else {
            const errorMsg = res.data.message || '认证失败，请重试'
            this.handleAuthError(errorMsg)
            reject(new Error(errorMsg))
          }
        },
        fail: (err) => {
          console.error('认证API请求失败:', err)
          wx.hideLoading()

          // 在开发测试阶段，如果API不可用，提供测试模式
          if (err.errMsg && err.errMsg.includes('request:fail')) {
            console.log('认证API不可用，提供测试选项')
            this.showTestModeOption(deviceInfo, resolve, reject)
          } else {
            const errorMsg = '网络请求失败，请检查网络连接'
            this.handleAuthError(errorMsg)
            reject(new Error(errorMsg))
          }
        }
      })
    })
  },

  // 显示测试模式选项
  showTestModeOption: function(deviceInfo, resolve, reject) {
    wx.showModal({
      title: '认证服务不可用',
      content: '无法连接到认证服务器\n\n是否使用测试模式继续？',
      showCancel: true,
      confirmText: '测试模式',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 模拟成功认证
          this.setData({
            isAuthenticating: false,
            authStatus: 'success',
            authStatusMessage: '认证成功！（测试模式）您现在可以正常使用网络了。'
          })

          wx.showToast({
            title: '认证成功（测试）',
            icon: 'success'
          })

          // 保存测试认证状态
          const authStatus = {
            status: 'success',
            authenticated: true,
            timestamp: Date.now(),
            openid: deviceInfo.openid,
            ip: deviceInfo.ip,
            ssid: deviceInfo.ssid,
            testMode: true,
            expires: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
          }

          wx.setStorageSync('wifiAuthStatus', authStatus)
          resolve(authStatus)
        } else {
          this.setData({
            isAuthenticating: false
          })
          reject(new Error('用户取消认证'))
        }
      }
    })
  },

  // 处理认证错误
  handleAuthError: function (message) {
    this.setData({
      isAuthenticating: false,
      authStatus: 'error',
      authStatusMessage: message || '认证失败，请检查网络连接后重试'
    })

    wx.showToast({
      title: '认证失败',
      icon: 'error'
    })
  },

  // 重新认证
  retryAuth: function () {
    this.setData({
      authStatus: null,
      authStatusMessage: ''
    })
    this.performAuthentication()
  },

  // 测试网络连接
  testNetwork: function () {
    wx.showLoading({
      title: '测试网络...'
    })

    NetworkDiagnostic.testInternetConnection()
      .then((result) => {
        wx.hideLoading()
        if (result.success) {
          wx.showModal({
            title: '网络测试',
            content: `网络连接正常\n延迟: ${result.latency}ms\n下载速度: ${result.downloadSpeed}`,
            showCancel: false
          })
        } else {
          wx.showModal({
            title: '网络测试',
            content: '网络连接异常，请检查网络设置',
            showCancel: false
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络测试失败',
          icon: 'error'
        })
      })
  },

  // 显示使用协议
  showTermsModal: function () {
    this.setData({
      showTermsModal: true
    })
  },

  // 隐藏使用协议
  hideTermsModal: function () {
    this.setData({
      showTermsModal: false
    })
  },

  // 同意协议
  agreeTerms: function () {
    this.setData({
      showTermsModal: false,
      hasAgreedTerms: true
    })
    
    // 保存协议同意状态
    wx.setStorageSync('terms_agreed', true)
    
    // 开始认证
    this.performAuthentication()
  },

  onReady: function () {
    // 检查是否已同意协议
    const termsAgreed = wx.getStorageSync('terms_agreed')
    if (termsAgreed) {
      this.setData({
        hasAgreedTerms: true
      })
    }
  },

  onUnload: function () {
    // 页面卸载时清理
    wx.hideLoading()
  }
})