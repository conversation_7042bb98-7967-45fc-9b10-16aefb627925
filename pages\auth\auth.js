const NetworkDiagnostic = require('../../utils/network-diagnostic.js')

Page({
  data: {
    currentWifi: null,
    userInfo: null,
    openid: '',
    isAuthenticating: false,
    authStatus: null, // 'success' | 'error' | null
    authStatusMessage: '',
    showTermsModal: false,
    hasAgreedTerms: false
  },

  onLoad: function (options) {
    console.log('Auth page loaded')
    this.getCurrentWifiInfo()
    this.getUserInfo()
    this.getOpenId()
  },

  onShow: function () {
    // 页面显示时刷新WiFi信息
    this.getCurrentWifiInfo()
  },

  // 获取当前WiFi信息
  getCurrentWifiInfo: function () {
    const app = getApp()
    if (app.globalData.currentWifi) {
      this.setData({
        currentWifi: app.globalData.currentWifi
      })
    } else {
      // 尝试重新获取WiFi信息
      wx.getConnectedWifi({
        success: (res) => {
          console.log('获取WiFi信息成功:', res.wifi)
          this.setData({
            currentWifi: res.wifi
          })
          app.globalData.currentWifi = res.wifi
        },
        fail: (err) => {
          console.error('获取WiFi信息失败:', err)
          wx.showToast({
            title: '无法获取WiFi信息',
            icon: 'none'
          })
        }
      })
    }
  },

  // 获取用户信息
  getUserInfo: function () {
    const app = getApp()
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    }
  },

  // 获取OpenID
  getOpenId: function () {
    const app = getApp()
    if (app.globalData.openid) {
      this.setData({
        openid: app.globalData.openid
      })
    } else {
      // 调用云函数获取OpenID
      wx.cloud.callFunction({
        name: 'getOpenId',
        success: (res) => {
          console.log('获取OpenID成功:', res.result.openid)
          this.setData({
            openid: res.result.openid
          })
          app.globalData.openid = res.result.openid
        },
        fail: (err) => {
          console.error('获取OpenID失败:', err)
          // 使用模拟OpenID用于测试
          const mockOpenId = 'mock_' + Date.now().toString(36)
          this.setData({
            openid: mockOpenId
          })
          app.globalData.openid = mockOpenId
        }
      })
    }
  },

  // 开始微信认证
  startWechatAuth: function () {
    if (!this.data.hasAgreedTerms) {
      this.setData({
        showTermsModal: true
      })
      return
    }

    if (!this.data.currentWifi) {
      wx.showToast({
        title: '请先连接WiFi网络',
        icon: 'none'
      })
      return
    }

    if (!this.data.openid) {
      wx.showToast({
        title: '正在获取用户信息...',
        icon: 'loading'
      })
      return
    }

    this.performAuthentication()
  },

  // 执行认证
  performAuthentication: function () {
    this.setData({
      isAuthenticating: true,
      authStatus: null
    })

    wx.showLoading({
      title: '认证中...'
    })

    // 模拟认证过程
    setTimeout(() => {
      this.authenticateWithMikroTik()
    }, 1000)
  },

  // MikroTik认证
  authenticateWithMikroTik: function () {
    const authData = {
      username: this.data.openid,
      password: 'wechat_auth_' + Date.now(),
      mac: this.data.currentWifi.BSSID || '',
      ip: '*************', // 模拟IP
      ssid: this.data.currentWifi.SSID || ''
    }

    console.log('认证数据:', authData)

    // 模拟认证请求
    wx.request({
      url: 'https://your-mikrotik-server.com/auth', // 替换为实际的认证服务器地址
      method: 'POST',
      data: authData,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('认证响应:', res)
        wx.hideLoading()
        
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            isAuthenticating: false,
            authStatus: 'success',
            authStatusMessage: '认证成功！您现在可以正常使用网络了。'
          })
          
          wx.showToast({
            title: '认证成功',
            icon: 'success'
          })

          // 保存认证状态
          wx.setStorageSync('auth_status', {
            authenticated: true,
            timestamp: Date.now(),
            openid: this.data.openid
          })

        } else {
          this.handleAuthError('认证失败，请重试')
        }
      },
      fail: (err) => {
        console.error('认证请求失败:', err)
        wx.hideLoading()
        
        // 模拟成功认证用于测试
        this.setData({
          isAuthenticating: false,
          authStatus: 'success',
          authStatusMessage: '认证成功！（测试模式）您现在可以正常使用网络了。'
        })
        
        wx.showToast({
          title: '认证成功（测试）',
          icon: 'success'
        })
      }
    })
  },

  // 处理认证错误
  handleAuthError: function (message) {
    this.setData({
      isAuthenticating: false,
      authStatus: 'error',
      authStatusMessage: message || '认证失败，请检查网络连接后重试'
    })

    wx.showToast({
      title: '认证失败',
      icon: 'error'
    })
  },

  // 重新认证
  retryAuth: function () {
    this.setData({
      authStatus: null,
      authStatusMessage: ''
    })
    this.performAuthentication()
  },

  // 测试网络连接
  testNetwork: function () {
    wx.showLoading({
      title: '测试网络...'
    })

    NetworkDiagnostic.testInternetConnection()
      .then((result) => {
        wx.hideLoading()
        if (result.success) {
          wx.showModal({
            title: '网络测试',
            content: `网络连接正常\n延迟: ${result.latency}ms\n下载速度: ${result.downloadSpeed}`,
            showCancel: false
          })
        } else {
          wx.showModal({
            title: '网络测试',
            content: '网络连接异常，请检查网络设置',
            showCancel: false
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络测试失败',
          icon: 'error'
        })
      })
  },

  // 显示使用协议
  showTermsModal: function () {
    this.setData({
      showTermsModal: true
    })
  },

  // 隐藏使用协议
  hideTermsModal: function () {
    this.setData({
      showTermsModal: false
    })
  },

  // 同意协议
  agreeTerms: function () {
    this.setData({
      showTermsModal: false,
      hasAgreedTerms: true
    })
    
    // 保存协议同意状态
    wx.setStorageSync('terms_agreed', true)
    
    // 开始认证
    this.performAuthentication()
  },

  onReady: function () {
    // 检查是否已同意协议
    const termsAgreed = wx.getStorageSync('terms_agreed')
    if (termsAgreed) {
      this.setData({
        hasAgreedTerms: true
      })
    }
  },

  onUnload: function () {
    // 页面卸载时清理
    wx.hideLoading()
  }
})