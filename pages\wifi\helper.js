// WiFi连接帮助工具
const wifiHelper = {
  // 获取连接建议
  getConnectionAdvice(wifi, error) {
    const advice = {
      title: '连接建议',
      content: '',
      actions: []
    }

    if (!wifi) {
      advice.content = '请选择一个有效的WiFi网络'
      return advice
    }

    // 根据WiFi类型和错误提供建议
    if (wifi.secure) {
      // 安全网络
      if (error && error.errCode === 12002) {
        advice.title = '密码错误'
        advice.content = '请检查WiFi密码是否正确，注意区分大小写'
        advice.actions = ['重新输入密码', '联系网络管理员']
      } else {
        advice.content = '连接加密网络失败，请确认密码正确'
        advice.actions = ['检查密码', '重试连接']
      }
    } else {
      // 开放网络
      advice.title = '开放网络连接失败'
      advice.content = '连接开放网络失败，可能需要额外的认证步骤'
      advice.actions = ['重试连接', '检查网络状态', '联系技术支持']
    }

    return advice
  },

  // 检查网络环境
  checkNetworkEnvironment() {
    return new Promise((resolve) => {
      const result = {
        wifi: false,
        location: false,
        system: null
      }

      // 检查WiFi状态
      wx.getSystemInfo({
        success: (res) => {
          result.system = {
            platform: res.platform,
            system: res.system,
            version: res.version,
            SDKVersion: res.SDKVersion
          }

          // 检查网络类型
          wx.getNetworkType({
            success: (netRes) => {
              result.wifi = netRes.networkType === 'wifi'
              
              // 检查位置权限
              wx.getSetting({
                success: (settingRes) => {
                  result.location = settingRes.authSetting['scope.userLocation'] === true
                  resolve(result)
                },
                fail: () => resolve(result)
              })
            },
            fail: () => resolve(result)
          })
        },
        fail: () => resolve(result)
      })
    })
  },

  // 显示连接帮助
  showConnectionHelp(wifi, error) {
    const advice = this.getConnectionAdvice(wifi, error)
    
    wx.showModal({
      title: advice.title,
      content: advice.content,
      showCancel: true,
      confirmText: '我知道了',
      cancelText: '查看详情',
      success: (res) => {
        if (!res.confirm) {
          this.showDetailedHelp(wifi, error)
        }
      }
    })
  },

  // 显示详细帮助
  showDetailedHelp(wifi, error) {
    let content = '连接WiFi详细帮助：\n\n'
    
    if (wifi) {
      content += `网络名称：${wifi.SSID}\n`
      content += `网络类型：${wifi.secure ? '加密网络' : '开放网络'}\n`
      content += `信号强度：${wifi.signalStrength}%\n\n`
    }

    content += '常见问题解决方案：\n'
    content += '1. 确保手机WiFi功能已开启\n'
    content += '2. 确保GPS定位服务已开启\n'
    content += '3. 检查是否在网络覆盖范围内\n'
    content += '4. 尝试忘记网络后重新连接\n'
    content += '5. 重启手机WiFi功能\n\n'

    if (error) {
      content += `错误信息：${error.errMsg || '未知错误'}\n`
      content += `错误代码：${error.errCode || '无'}`
    }

    wx.showModal({
      title: '详细帮助',
      content: content,
      showCancel: false,
      confirmText: '关闭'
    })
  },

  // 网络诊断
  // 网络诊断
  async diagnoseNetwork() {
    wx.showLoading({ title: '诊断中...' })
    
    try {
      const env = await this.checkNetworkEnvironment()
      
      // 检查认证状态
      const authStatus = wx.getStorageSync('wifiAuthStatus')
      
      wx.hideLoading()
      
      let diagnosis = '网络环境诊断结果：\n\n'
      diagnosis += `WiFi状态：${env.wifi ? '✓ 已连接' : '✗ 未连接'}\n`
      diagnosis += `位置权限：${env.location ? '✓ 已授权' : '✗ 未授权'}\n`
      
      // 认证状态
      if (authStatus && authStatus.status === 'success') {
        diagnosis += `认证状态：✓ 已认证\n`
        if (authStatus.expires) {
          diagnosis += `有效期至：${authStatus.expires}\n`
        }
        if (authStatus.logId) {
          diagnosis += `日志ID：${authStatus.logId}\n`
        }
      } else {
        diagnosis += `认证状态：✗ 未认证\n`
      }
      
      if (env.system) {
        diagnosis += `\n系统信息：\n`
        diagnosis += `平台：${env.system.platform}\n`
        diagnosis += `系统：${env.system.system}\n`
        diagnosis += `微信：${env.system.version}\n`
        diagnosis += `基础库：${env.system.SDKVersion}\n`
      }

      diagnosis += '\n建议：\n'
      if (!env.wifi) {
        diagnosis += '• 请先连接到WiFi网络\n'
      }
      if (!env.location) {
        diagnosis += '• 请授权位置权限\n'
      }
      if (!authStatus || authStatus.status !== 'success') {
        diagnosis += '• 请完成网络认证\n'
      }
      diagnosis += '• 确保在网络覆盖范围内\n'
      diagnosis += '• 检查网络设置是否正确'

      wx.showModal({
        title: '网络诊断',
        content: diagnosis,
        showCancel: false,
        confirmText: '关闭'
      })
    } catch (err) {
      wx.hideLoading()
      wx.showToast({
        title: '诊断失败',
        icon: 'none'
      })
    }
  }
}

module.exports = wifiHelper