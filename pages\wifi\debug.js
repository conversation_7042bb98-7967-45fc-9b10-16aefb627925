// WiFi调试工具
const debugWifi = {
  // 打印WiFi详细信息
  logWifiInfo(wifi) {
    console.log('=== WiFi详细信息 ===')
    console.log('SSID:', wifi.SSID)
    console.log('BSSID:', wifi.BSSID)
    console.log('安全类型:', wifi.secure ? '加密' : '开放')
    console.log('信号强度:', wifi.signalStrength + '%')
    console.log('频率:', wifi.frequency + 'MHz')
    console.log('原始对象:', wifi)
    console.log('==================')
  },

  // 检查连接参数
  validateConnectParams(params) {
    console.log('=== 连接参数检查 ===')
    console.log('SSID:', params.SSID, params.SSID ? '✓' : '✗')
    console.log('BSSID:', params.BSSID, params.BSSID ? '✓' : '可选')
    console.log('密码:', params.password !== undefined ? (params.password === '' ? '空密码(开放网络)' : '已设置') : '未定义', 
                params.password !== undefined ? '✓' : '✗')
    console.log('完整参数:', params)
    console.log('==================')
  },

  // 分析连接错误
  analyzeError(err) {
    console.log('=== 连接错误分析 ===')
    console.log('错误对象:', err)
    
    if (!err) {
      console.log('错误信息为空')
      return '未知错误'
    }

    if (err.errCode) {
      console.log('错误码:', err.errCode)
      console.log('错误信息:', err.errMsg)
    } else {
      console.log('无错误码，可能是系统级错误')
    }
    
    console.log('==================')
    return this.getErrorMessage(err)
  },

  // 获取友好的错误信息
  getErrorMessage(err) {
    if (!err) return '未知错误'
    
    // 检查是否是参数错误
    if (err.errMsg && err.errMsg.includes('parameter error')) {
      if (err.errMsg.includes('password should be String')) {
        return '参数错误：密码格式不正确'
      }
      return '参数错误：请检查连接参数'
    }
    
    const errorMap = {
      12000: 'WiFi未开启',
      12001: 'GPS定位未开启', 
      12002: '密码错误',
      12003: '连接超时',
      12004: '重复连接WiFi',
      12005: 'WiFi未打开',
      12006: 'GPS定位未打开',
      12007: '用户拒绝授权链接WiFi',
      12008: '无效SSID',
      12009: '系统运营商配置拒绝连接该WiFi',
      12010: '系统其他配置拒绝连接该WiFi',
      12011: '应用内部错误、请重试'
    }

    if (err.errCode && errorMap[err.errCode]) {
      return errorMap[err.errCode]
    }

    return err.errMsg || '连接失败'
  },

  // 系统信息检查
  checkSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        console.log('=== 系统信息 ===')
        console.log('系统:', res.system)
        console.log('平台:', res.platform)
        console.log('微信版本:', res.version)
        console.log('基础库版本:', res.SDKVersion)
        console.log('设备型号:', res.model)
        console.log('===============')
      }
    })
  },

  // 网络状态检查
  checkNetworkType() {
    wx.getNetworkType({
      success: (res) => {
        console.log('=== 网络状态 ===')
        console.log('网络类型:', res.networkType)
        console.log('是否计费网络:', res.isConnected)
        console.log('===============')
      }
    })
  }
}

module.exports = debugWifi