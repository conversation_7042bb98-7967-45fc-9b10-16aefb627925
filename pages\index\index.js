// pages/index/index.js
const app = getApp()

Page({
  data: {
    hospitalInfo: {},
    wifiStatus: 'disconnected', // connected, connecting, disconnected
    wifiStatusText: '未连接',
    currentWifi: null,
    isConnected: false,
    authStatus: null, // 认证状态
    authStatusText: '未认证'
  },

  onLoad() {
    console.log('首页加载')
    this.initData()
    this.checkWifiStatus()
  },

  onShow() {
    console.log('首页显示')
    this.checkWifiStatus()
    this.checkAuthStatus()
  },

  // 初始化数据
  initData() {
    this.setData({
      hospitalInfo: app.globalData.hospitalInfo
    })
  },

  // 检查WiFi连接状态
  checkWifiStatus() {
    wx.getConnectedWifi({
      success: (res) => {
        console.log('当前连接的WiFi:', res.wifi)
        this.setData({
          currentWifi: res.wifi,
          isConnected: true,
          wifiStatus: 'connected',
          wifiStatusText: '已连接'
        })
        app.globalData.currentWifi = res.wifi
        app.globalData.isConnected = true
      },
      fail: (err) => {
        console.log('获取WiFi信息失败:', err)
        this.setData({
          currentWifi: null,
          isConnected: false,
          wifiStatus: 'disconnected',
          wifiStatusText: '未连接',
          authStatus: null,
          authStatusText: '未认证'
        })
        app.globalData.currentWifi = null
        app.globalData.isConnected = false
      }
    })
  },

  // 检查认证状态
  checkAuthStatus() {
    const authStatus = wx.getStorageSync('wifiAuthStatus')
    if (authStatus && authStatus.status === 'success') {
      // 检查是否过期
      if (authStatus.expires) {
        const expireTime = new Date(authStatus.expires).getTime()
        const currentTime = new Date().getTime()
        
        if (currentTime < expireTime) {
          this.setData({
            authStatus: authStatus,
            authStatusText: '已认证'
          })
        } else {
          this.setData({
            authStatus: null,
            authStatusText: '认证已过期'
          })
          // 清除过期的认证状态
          wx.removeStorageSync('wifiAuthStatus')
        }
      } else {
        this.setData({
          authStatus: authStatus,
          authStatusText: '已认证'
        })
      }
    } else {
      this.setData({
        authStatus: null,
        authStatusText: '未认证'
      })
    }
  },

  // 跳转到WiFi页面
  goToWifi() {
    wx.switchTab({
      url: '/pages/wifi/wifi'
    })
  },

  // 跳转到认证页面 - "我要上网"按钮
  goToAuth() {
    // 1. 检查WiFi连接状态
    if (!this.data.isConnected) {
      wx.showModal({
        title: '需要连接WiFi',
        content: '请先连接到医院的WiFi网络才能进行认证',
        showCancel: true,
        confirmText: '去连接',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/wifi/wifi'
            })
          }
        }
      })
      return
    }

    // 2. 检查当前认证状态
    const authStatus = wx.getStorageSync('wifiAuthStatus')
    if (authStatus && authStatus.status === 'success') {
      // 检查是否过期
      if (authStatus.expires && Date.now() < authStatus.expires) {
        wx.showModal({
          title: '已完成认证',
          content: '您已经完成WiFi认证，可以正常使用网络了。\n\n是否要重新认证？',
          showCancel: true,
          confirmText: '重新认证',
          cancelText: '知道了',
          success: (res) => {
            if (res.confirm) {
              // 清除旧的认证状态
              wx.removeStorageSync('wifiAuthStatus')
              this.navigateToAuth()
            }
          }
        })
        return
      } else {
        // 认证已过期，清除状态
        wx.removeStorageSync('wifiAuthStatus')
        this.setData({
          authStatus: null,
          authStatusText: '认证已过期'
        })
      }
    }

    // 3. 显示认证说明并跳转
    this.showAuthGuide()
  },

  // 显示认证指引
  showAuthGuide() {
    wx.showModal({
      title: 'WiFi认证说明',
      content: '认证过程需要：\n\n1. 获取您的微信用户信息\n2. 收集设备网络信息\n3. 提交到认证服务器\n\n是否继续？',
      showCancel: true,
      confirmText: '开始认证',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.navigateToAuth()
        }
      }
    })
  },

  // 跳转到认证页面
  navigateToAuth() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    })
  },

  // 跳转到关于页面
  goToAbout() {
    wx.switchTab({
      url: '/pages/about/about'
    })
  },

  // 网络测速
  checkNetwork() {
    if (!this.data.isConnected) {
      wx.showToast({
        title: '请先连接WiFi',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '测速中...'
    })

    // 模拟网络测速
    setTimeout(() => {
      wx.hideLoading()
      const speed = Math.floor(Math.random() * 50) + 10 // 10-60 Mbps
      wx.showModal({
        title: '网络测速结果',
        content: `当前网速：${speed} Mbps\n网络状态：${speed > 30 ? '优秀' : speed > 15 ? '良好' : '一般'}`,
        showCancel: false
      })
    }, 2000)
  },

  // 显示帮助
  showHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '1. 确保手机WiFi功能已开启\n2. 选择医院提供的WiFi网络\n3. 按提示完成身份认证\n4. 如有问题请联系医院服务台',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新')
    this.checkWifiStatus()
    this.checkAuthStatus()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})