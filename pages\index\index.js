// pages/index/index.js
const app = getApp()

Page({
  data: {
    hospitalInfo: {},
    wifiStatus: 'disconnected', // connected, connecting, disconnected
    wifiStatusText: '未连接',
    currentWifi: null,
    isConnected: false,
    authStatus: null, // 认证状态
    authStatusText: '未认证'
  },

  onLoad() {
    console.log('首页加载')
    this.initData()
    this.checkWifiStatus()
  },

  onShow() {
    console.log('首页显示')
    this.checkWifiStatus()
    this.checkAuthStatus()
  },

  // 初始化数据
  initData() {
    this.setData({
      hospitalInfo: app.globalData.hospitalInfo
    })
  },

  // 检查WiFi连接状态
  checkWifiStatus() {
    wx.getConnectedWifi({
      success: (res) => {
        console.log('当前连接的WiFi:', res.wifi)
        this.setData({
          currentWifi: res.wifi,
          isConnected: true,
          wifiStatus: 'connected',
          wifiStatusText: '已连接'
        })
        app.globalData.currentWifi = res.wifi
        app.globalData.isConnected = true
      },
      fail: (err) => {
        console.log('获取WiFi信息失败:', err)
        this.setData({
          currentWifi: null,
          isConnected: false,
          wifiStatus: 'disconnected',
          wifiStatusText: '未连接',
          authStatus: null,
          authStatusText: '未认证'
        })
        app.globalData.currentWifi = null
        app.globalData.isConnected = false
      }
    })
  },

  // 检查认证状态
  checkAuthStatus() {
    const authStatus = wx.getStorageSync('wifiAuthStatus')
    if (authStatus && authStatus.status === 'success') {
      // 检查是否过期
      if (authStatus.expires) {
        const expireTime = new Date(authStatus.expires).getTime()
        const currentTime = new Date().getTime()
        
        if (currentTime < expireTime) {
          this.setData({
            authStatus: authStatus,
            authStatusText: '已认证'
          })
        } else {
          this.setData({
            authStatus: null,
            authStatusText: '认证已过期'
          })
          // 清除过期的认证状态
          wx.removeStorageSync('wifiAuthStatus')
        }
      } else {
        this.setData({
          authStatus: authStatus,
          authStatusText: '已认证'
        })
      }
    } else {
      this.setData({
        authStatus: null,
        authStatusText: '未认证'
      })
    }
  },

  // 跳转到WiFi页面
  goToWifi() {
    wx.switchTab({
      url: '/pages/wifi/wifi'
    })
  },

  // 跳转到认证页面
  goToAuth() {
    if (!this.data.isConnected) {
      wx.showToast({
        title: '请先连接WiFi',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/auth/auth'
    })
  },

  // 跳转到关于页面
  goToAbout() {
    wx.switchTab({
      url: '/pages/about/about'
    })
  },

  // 网络测速
  checkNetwork() {
    if (!this.data.isConnected) {
      wx.showToast({
        title: '请先连接WiFi',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '测速中...'
    })

    // 模拟网络测速
    setTimeout(() => {
      wx.hideLoading()
      const speed = Math.floor(Math.random() * 50) + 10 // 10-60 Mbps
      wx.showModal({
        title: '网络测速结果',
        content: `当前网速：${speed} Mbps\n网络状态：${speed > 30 ? '优秀' : speed > 15 ? '良好' : '一般'}`,
        showCancel: false
      })
    }, 2000)
  },

  // 显示帮助
  showHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '1. 确保手机WiFi功能已开启\n2. 选择医院提供的WiFi网络\n3. 按提示完成身份认证\n4. 如有问题请联系医院服务台',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新')
    this.checkWifiStatus()
    this.checkAuthStatus()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})