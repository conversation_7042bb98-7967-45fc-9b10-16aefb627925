# MikroTik RouterOS WiFi认证集成说明

## 概述

本文档说明如何将康华医院WiFi认证小程序与MikroTik RouterOS 7.15.3集成，实现基于微信OpenID的WiFi认证。

## 系统架构

```
微信小程序 → 后端API服务器 → MikroTik RouterOS → 用户设备上网
```

## MikroTik RouterOS配置

### 1. 启用Hotspot功能

```bash
# 创建Hotspot服务器配置
/ip hotspot profile
add dns-name=kanghua-wifi.local hotspot-address=*********** html-directory=hotspot login-by=http-chap name=hsprof1

# 创建Hotspot服务器
/ip hotspot
add address-pool=hs-pool-1 disabled=no interface=wlan1 name=hotspot1 profile=hsprof1

# 配置地址池
/ip pool
add name=hs-pool-1 ranges=*************-*************
```

### 2. 配置用户认证

```bash
# 创建用户组
/ip hotspot user profile
add name=wechat-users rate-limit=10M/10M session-timeout=24h

# 启用API服务
/ip service
set api disabled=no port=8728

# 创建API用户
/user
add name=api-user password=your-secure-password group=full
```

### 3. 配置防火墙规则

```bash
# 允许API访问
/ip firewall filter
add action=accept chain=input dst-port=8728 protocol=tcp src-address=your-backend-server-ip

# 允许HTTP重定向
add action=accept chain=input dst-port=80 protocol=tcp
```

## 后端API实现

### Node.js + Express 示例

```javascript
const express = require('express');
const RouterOSAPI = require('node-routeros').RouterOSAPI;
const app = express();

// MikroTik连接配置
const routerConfig = {
  host: '***********',
  user: 'api-user',
  password: 'your-secure-password',
  port: 8728
};

// 微信认证接口
app.post('/auth/mikrotik', async (req, res) => {
  try {
    const { openid, ssid, mac } = req.body;
    
    // 验证OpenID（可选：调用微信API验证）
    if (!openid || openid.length < 20) {
      return res.json({ success: false, message: '无效的OpenID' });
    }
    
    // 连接MikroTik
    const conn = new RouterOSAPI(routerConfig);
    await conn.connect();
    
    // 创建或更新用户
    const username = `wx_${openid.substring(0, 16)}`;
    const password = generateRandomPassword();
    
    // 检查用户是否存在
    const existingUsers = await conn.write('/ip/hotspot/user/print', [
      `?name=${username}`
    ]);
    
    if (existingUsers.length > 0) {
      // 更新现有用户
      await conn.write('/ip/hotspot/user/set', [
        `=.id=${existingUsers[0]['.id']}`,
        `=password=${password}`,
        '=profile=wechat-users'
      ]);
    } else {
      // 创建新用户
      await conn.write('/ip/hotspot/user/add', [
        `=name=${username}`,
        `=password=${password}`,
        '=profile=wechat-users',
        `=comment=WeChat User: ${openid}`
      ]);
    }
    
    // 如果提供了MAC地址，添加到活跃用户
    if (mac) {
      await conn.write('/ip/hotspot/active/login', [
        `=user=${username}`,
        `=password=${password}`,
        `=mac-address=${mac}`
      ]);
    }
    
    await conn.close();
    
    res.json({
      success: true,
      message: '认证成功',
      username: username,
      password: password
    });
    
  } catch (error) {
    console.error('认证失败:', error);
    res.json({
      success: false,
      message: '认证失败，请稍后重试'
    });
  }
});

// 生成随机密码
function generateRandomPassword() {
  return Math.random().toString(36).substring(2, 15);
}

app.listen(3000, () => {
  console.log('API服务器运行在端口 3000');
});
```

### Python + Flask 示例

```python
from flask import Flask, request, jsonify
import librouteros
import random
import string

app = Flask(__name__)

# MikroTik连接配置
ROUTER_CONFIG = {
    'host': '***********',
    'username': 'api-user',
    'password': 'your-secure-password',
    'port': 8728
}

@app.route('/auth/mikrotik', methods=['POST'])
def authenticate_wechat():
    try:
        data = request.json
        openid = data.get('openid')
        ssid = data.get('ssid')
        mac = data.get('mac')
        
        # 验证OpenID
        if not openid or len(openid) < 20:
            return jsonify({'success': False, 'message': '无效的OpenID'})
        
        # 连接MikroTik
        api = librouteros.connect(**ROUTER_CONFIG)
        
        # 创建用户名和密码
        username = f"wx_{openid[:16]}"
        password = generate_random_password()
        
        # 检查用户是否存在
        users = list(api.path('ip', 'hotspot', 'user').select('name').where('name', username))
        
        if users:
            # 更新现有用户
            api.path('ip', 'hotspot', 'user').update(**{
                '.id': users[0]['.id'],
                'password': password,
                'profile': 'wechat-users'
            })
        else:
            # 创建新用户
            api.path('ip', 'hotspot', 'user').add(
                name=username,
                password=password,
                profile='wechat-users',
                comment=f'WeChat User: {openid}'
            )
        
        # 如果提供了MAC地址，添加到活跃用户
        if mac:
            api.path('ip', 'hotspot', 'active').add(
                user=username,
                password=password,
                **{'mac-address': mac}
            )
        
        return jsonify({
            'success': True,
            'message': '认证成功',
            'username': username,
            'password': password
        })
        
    except Exception as e:
        print(f'认证失败: {e}')
        return jsonify({
            'success': False,
            'message': '认证失败，请稍后重试'
        })

def generate_random_password():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=12))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)
```

## 小程序配置

### 1. 修改API地址

在 `pages/auth/auth.js` 中修改API地址：

```javascript
wx.request({
  url: 'https://your-domain.com/auth/mikrotik', // 替换为您的API地址
  method: 'POST',
  data: authData,
  // ...
})
```

### 2. 获取设备MAC地址（可选）

```javascript
// 获取设备信息
wx.getSystemInfo({
  success: (res) => {
    console.log('设备信息:', res);
    // 注意：小程序无法直接获取MAC地址
    // 可以使用设备ID作为替代
    const deviceId = res.system + '_' + res.model;
    // 在认证时传递设备ID
  }
})
```

## 安全考虑

### 1. HTTPS通信
- 后端API必须使用HTTPS
- 小程序只能调用HTTPS接口

### 2. 数据加密
```javascript
// 对敏感数据进行加密传输
const crypto = require('crypto');

function encryptData(data, key) {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}
```

### 3. 访问控制
- 限制API访问频率
- 验证请求来源
- 记录认证日志

## 部署步骤

### 1. 配置MikroTik RouterOS
- 按照上述配置设置Hotspot
- 创建API用户和权限
- 配置防火墙规则

### 2. 部署后端API
- 安装Node.js或Python环境
- 部署API服务器
- 配置HTTPS证书
- 设置域名解析

### 3. 配置小程序
- 修改API地址
- 配置服务器域名白名单
- 测试认证流程

### 4. 测试验证
- 测试WiFi连接
- 验证认证流程
- 检查用户管理
- 监控系统日志

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查MikroTik API服务是否启用
   - 验证防火墙规则
   - 确认用户权限

2. **认证失败**
   - 检查用户配置
   - 验证密码生成
   - 查看MikroTik日志

3. **网络不通**
   - 检查Hotspot配置
   - 验证地址池设置
   - 确认路由规则

### 日志监控

```bash
# 查看Hotspot日志
/log print where topics~"hotspot"

# 查看用户活动
/ip hotspot active print

# 查看用户列表
/ip hotspot user print
```

## 扩展功能

### 1. 用户管理
- 用户使用统计
- 流量监控
- 时间限制

### 2. 计费系统
- 按时间计费
- 按流量计费
- 套餐管理

### 3. 监控报警
- 连接数监控
- 流量异常报警
- 系统状态监控

---

**注意**：请根据实际网络环境调整配置参数，确保网络安全和稳定运行。