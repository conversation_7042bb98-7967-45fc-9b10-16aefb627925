/**app.wxss**/
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', '<PERSON>buntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  background-color: #2E8B57;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  border: none;
  margin: 20rpx 0;
}

.btn:active {
  background-color: #228B22;
}

.btn-disabled {
  background-color: #cccccc;
  color: #999999;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

/* 文本样式 */
.text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

.text-secondary {
  font-size: 24rpx;
  color: #999999;
}

/* 图标样式 */
.icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.list-item:last-child {
  border-bottom: none;
}

/* 状态指示器 */
.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.status-connected {
  background-color: #2E8B57;
}

.status-disconnected {
  background-color: #cccccc;
}

.status-connecting {
  background-color: #FFA500;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999999;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

/* 医院主题色 */
.hospital-primary {
  color: #2E8B57;
}

.hospital-bg {
  background-color: #2E8B57;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .card {
    padding: 20rpx;
    margin: 15rpx 0;
  }
}