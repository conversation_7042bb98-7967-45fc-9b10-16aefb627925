<?php
// api-get-openid.php - 获取微信小程序用户OpenID
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只支持POST请求'
    ]);
    exit();
}

// 获取请求数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// 记录请求日志
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $_SERVER['REMOTE_ADDR'],
    'request_data' => $data
];
file_put_contents('openid_requests.log', json_encode($logData) . "\n", FILE_APPEND);

// 验证必需参数
if (!isset($data['code']) || empty($data['code'])) {
    echo json_encode([
        'success' => false,
        'message' => '缺少code参数'
    ]);
    exit();
}

// 微信小程序配置 - 请替换为您的真实配置
$appid = 'wxfb327da0d29574ca';  // 您的小程序AppID
$secret = 'your_app_secret_here'; // 您的小程序AppSecret

// 验证配置
if ($secret === 'your_app_secret_here') {
    echo json_encode([
        'success' => false,
        'message' => '请先配置小程序AppSecret'
    ]);
    exit();
}

try {
    // 调用微信接口获取session_key和openid
    $url = "https://api.weixin.qq.com/sns/jscode2session";
    $params = [
        'appid' => $appid,
        'secret' => $secret,
        'js_code' => $data['code'],
        'grant_type' => 'authorization_code'
    ];
    
    $requestUrl = $url . '?' . http_build_query($params);
    
    // 发送请求
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => 'User-Agent: KangHua-Hospital-WiFi-Auth/1.0'
        ]
    ]);
    
    $response = file_get_contents($requestUrl, false, $context);
    
    if ($response === false) {
        throw new Exception('请求微信接口失败');
    }
    
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('解析微信接口响应失败');
    }
    
    // 检查微信接口返回结果
    if (isset($result['errcode'])) {
        $errorMessages = [
            40013 => 'AppID无效',
            40125 => 'AppSecret无效',
            40029 => 'code无效',
            45011 => 'API调用太频繁，请稍后再试',
            40226 => 'code已被使用'
        ];
        
        $errorMsg = $errorMessages[$result['errcode']] ?? '微信接口错误: ' . $result['errmsg'];
        throw new Exception($errorMsg);
    }
    
    // 成功获取openid
    if (isset($result['openid'])) {
        // 可选：将用户信息保存到数据库
        // saveUserToDatabase($result['openid'], $result['session_key']);
        
        echo json_encode([
            'success' => true,
            'openid' => $result['openid'],
            'session_key' => $result['session_key'], // 注意：session_key很敏感，根据需要决定是否返回
            'message' => '获取OpenID成功'
        ]);
        
        // 记录成功日志
        $successLog = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'],
            'openid' => $result['openid'],
            'status' => 'success'
        ];
        file_put_contents('openid_success.log', json_encode($successLog) . "\n", FILE_APPEND);
        
    } else {
        throw new Exception('微信接口未返回openid');
    }
    
} catch (Exception $e) {
    // 错误处理
    $errorLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'error' => $e->getMessage(),
        'code' => $data['code']
    ];
    file_put_contents('openid_errors.log', json_encode($errorLog) . "\n", FILE_APPEND);
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 将用户信息保存到数据库（可选）
 */
function saveUserToDatabase($openid, $sessionKey) {
    try {
        // 数据库连接配置
        $host = 'localhost';
        $dbname = 'kanghua_wifi';
        $username = 'your_db_user';
        $password = 'your_db_password';
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查用户是否已存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE openid = ?");
        $stmt->execute([$openid]);
        
        if ($stmt->rowCount() == 0) {
            // 新用户，插入记录
            $stmt = $pdo->prepare("INSERT INTO users (openid, session_key, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
            $stmt->execute([$openid, $sessionKey]);
        } else {
            // 已存在用户，更新session_key
            $stmt = $pdo->prepare("UPDATE users SET session_key = ?, updated_at = NOW() WHERE openid = ?");
            $stmt->execute([$sessionKey, $openid]);
        }
        
    } catch (PDOException $e) {
        error_log("数据库操作失败: " . $e->getMessage());
    }
}
?>